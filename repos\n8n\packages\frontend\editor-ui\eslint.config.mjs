import { defineConfig } from 'eslint/config';
import { frontendConfig } from '@n8n/eslint-config/frontend';

export default defineConfig(frontendConfig, {
	rules: {
		// TODO: Remove these
		'@typescript-eslint/ban-ts-comment': ['warn', { 'ts-ignore': true }],
		'id-denylist': 'warn',
		'no-case-declarations': 'warn',
		'no-useless-escape': 'warn',
		'no-prototype-builtins': 'warn',
		'no-empty': 'warn',
		'no-fallthrough': 'warn',
		'no-extra-boolean-cast': 'warn',
		'no-sparse-arrays': 'warn',
		'no-control-regex': 'warn',
		'import-x/extensions': 'warn',
		'import-x/no-default-export': 'warn',
		'import-x/order': 'off',
		'import-x/no-cycle': 'warn',
		'import-x/no-duplicates': 'warn',
		'no-unsafe-optional-chaining': 'warn',
		'@typescript-eslint/no-restricted-types': 'warn',
		'@typescript-eslint/dot-notation': 'warn',
		'@stylistic/lines-between-class-members': 'warn',
		'@stylistic/member-delimiter-style': 'warn',
		'@typescript-eslint/naming-convention': 'off',
		'@typescript-eslint/no-empty-interface': 'warn',
		'@typescript-eslint/no-for-in-array': 'warn',
		'@typescript-eslint/no-loop-func': 'warn',
		'@typescript-eslint/no-non-null-assertion': 'warn',
		'@typescript-eslint/no-shadow': 'warn',
		'@typescript-eslint/no-this-alias': 'warn',
		'@typescript-eslint/no-unnecessary-boolean-literal-compare': 'warn',
		'@typescript-eslint/no-unnecessary-type-assertion': 'warn',
		'@typescript-eslint/no-unused-expressions': 'warn',
		'@typescript-eslint/no-unused-vars': 'warn',
		'@typescript-eslint/no-var-requires': 'warn',
		'@typescript-eslint/prefer-nullish-coalescing': 'warn',
		'@typescript-eslint/prefer-optional-chain': 'warn',
		'@typescript-eslint/restrict-plus-operands': 'warn',
		'@typescript-eslint/no-redundant-type-constituents': 'warn',
		'@typescript-eslint/no-unsafe-enum-comparison': 'warn',
		'@typescript-eslint/require-await': 'warn',
		'@typescript-eslint/prefer-promise-reject-errors': 'warn',
		'@typescript-eslint/no-base-to-string': 'warn',
		'@typescript-eslint/no-empty-object-type': 'warn',
		'@typescript-eslint/no-unsafe-function-type': 'warn',
		'vue/attribute-hyphenation': 'warn',
		'@typescript-eslint/no-unsafe-assignment': 'warn',
		'@typescript-eslint/unbound-method': 'warn',
		'@typescript-eslint/restrict-template-expressions': 'warn',
		'@typescript-eslint/no-unsafe-call': 'warn',
		'@typescript-eslint/no-unsafe-argument': 'warn',
		'@typescript-eslint/no-unsafe-member-access': 'warn',
		'@typescript-eslint/no-unsafe-return': 'warn',
	},
});
