{"logtail":{"client_time":"2025-07-17T17:41:37.543725059Z","proc_id":**********,"proc_seq":1},"text":"logtail started"}
{"logtail":{"client_time":"2025-07-17T17:41:37.547389439Z","proc_id":**********,"proc_seq":2},"text":"Program starting: v1.84.3-t7648989bc, Go 1.24.2: []string{\"tailscaled\", \"--socket=/tmp/tailscaled.sock\", \"--statedir=/var/lib/tailscale\"}\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.54761554Z","proc_id":**********,"proc_seq":3},"text":"LogID: 273e454a9471b565e078062ac7e0d04bc247e138030736cd6712d0a453341148\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.547787369Z","proc_id":**********,"proc_seq":4},"text":"logpolicy: using system state directory \"/var/lib/tailscale\"\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.548073731Z","proc_id":**********,"proc_seq":5},"text":"dns: [rc=unknown ret=direct]\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.548286947Z","proc_id":**********,"proc_seq":6},"text":"dns: using \"direct\" mode\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.548472085Z","proc_id":**********,"proc_seq":7},"text":"dns: using *dns.directManager\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.548767429Z","proc_id":**********,"proc_seq":8},"text":"dns: inotify: NewDirWatcher: context canceled\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.56210192Z","proc_id":**********,"proc_seq":9},"text":"wgengine.NewUserspaceEngine(tun \"tailscale0\") ...\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.570488756Z","proc_id":**********,"proc_seq":10},"text":"setting link attributes: netlink receive: no such file or directory\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.570744531Z","proc_id":**********,"proc_seq":11},"v":1,"text":"router: policy routing available; found 9 rules\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.570994428Z","proc_id":**********,"proc_seq":12},"v":1,"text":"router: kernel supports IPv6 policy routing (found 7 rules)\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.571243595Z","proc_id":**********,"proc_seq":13},"text":"dns: [rc=unknown ret=direct]\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.571469126Z","proc_id":**********,"proc_seq":14},"text":"dns: using \"direct\" mode\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.573236639Z","proc_id":**********,"proc_seq":15},"text":"dns: using *dns.directManager\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.573485717Z","proc_id":**********,"proc_seq":16},"text":"link state: interfaces.State{defaultRoute=eth0 ifs={br-1c0fa1572040:[**********/16 llu6] br-ab00c5797122:[**********/16 llu6] br-ac85f49a1a43:[**********/16] br-d3f50c987d01:[**********/16 llu6] docker0:[**********/16] eth0:[************/24 fdc4:f303:9324::3/64 llu6] services1:[************/32 fdc4:f303:9324::6/128 llu6]} v4=true v6=true}\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.573742934Z","proc_id":**********,"proc_seq":17},"text":"onPortUpdate(port=34724, network=udp6)\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.573895413Z","proc_id":**********,"proc_seq":18},"text":"router: using firewall mode pref \n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.575030019Z","proc_id":**********,"proc_seq":19},"text":"router: default choosing iptables\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.576182634Z","proc_id":**********,"proc_seq":20},"v":1,"text":"router: kernel supports IPv6 policy routing (found 7 rules)\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.57759732Z","proc_id":**********,"proc_seq":21},"text":"router: netfilter running in iptables mode v6 = true, v6filter = true, v6nat = true\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.578452415Z","proc_id":**********,"proc_seq":22},"text":"onPortUpdate(port=55619, network=udp4)\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.57863409Z","proc_id":**********,"proc_seq":23},"v":1,"text":"magicsock: peermtu: peer MTU status is false\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.578787855Z","proc_id":**********,"proc_seq":24},"text":"magicsock: disco key = d:96224f5c01ae6d48\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.578963326Z","proc_id":**********,"proc_seq":25},"text":"Creating WireGuard device...\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.579386567Z","proc_id":**********,"proc_seq":26},"text":"Bringing WireGuard device up...\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.********1Z","proc_id":**********,"proc_seq":27},"v":2,"text":"wg: UDP bind has been updated\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.579748853Z","proc_id":**********,"proc_seq":28},"v":2,"text":"wg: Interface state was Down, requested Up, now Up\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.579966558Z","proc_id":**********,"proc_seq":29},"text":"Bringing router up...\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.580164605Z","proc_id":**********,"proc_seq":30},"v":2,"text":"wg: Routine: receive incoming mkReceiveFunc - started\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.580687154Z","proc_id":**********,"proc_seq":31},"text":"external route: up\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.580832036Z","proc_id":**********,"proc_seq":32},"v":2,"text":"wg: Routine: receive incoming receiveDERP - started\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.581359971Z","proc_id":**********,"proc_seq":33},"v":2,"text":"wg: Routine: receive incoming mkReceiveFunc - started\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.581599197Z","proc_id":**********,"proc_seq":34},"text":"Clearing router settings...\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.581755742Z","proc_id":**********,"proc_seq":35},"text":"Starting network monitor...\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.58188855Z","proc_id":**********,"proc_seq":36},"text":"Engine created.\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.582119414Z","proc_id":**********,"proc_seq":37},"text":"monitor: ip rule deleted: {Family:2 DstLength:0 SrcLength:0 Tos:0 Table:254 Protocol:0 Scope:0 Type:1 Flags:0 Attributes:{Dst:<nil> Src:<nil> Gateway:<nil> OutIface:0 Priority:5210 Table:254 Mark:16711680 Pref:<nil> Expires:<nil> Metrics:<nil> Multipath:[]}}\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.582605496Z","proc_id":**********,"proc_seq":38},"text":"monitor: ip rule deleted: {Family:2 DstLength:0 SrcLength:0 Tos:0 Table:253 Protocol:0 Scope:0 Type:1 Flags:0 Attributes:{Dst:<nil> Src:<nil> Gateway:<nil> OutIface:0 Priority:5230 Table:253 Mark:16711680 Pref:<nil> Expires:<nil> Metrics:<nil> Multipath:[]}}\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.582834853Z","proc_id":**********,"proc_seq":39},"text":"monitor: ip rule deleted: {Family:2 DstLength:0 SrcLength:0 Tos:0 Table:0 Protocol:0 Scope:0 Type:7 Flags:0 Attributes:{Dst:<nil> Src:<nil> Gateway:<nil> OutIface:0 Priority:5250 Table:0 Mark:16711680 Pref:<nil> Expires:<nil> Metrics:<nil> Multipath:[]}}\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.583005129Z","proc_id":**********,"proc_seq":40},"text":"monitor: ip rule deleted: {Family:2 DstLength:0 SrcLength:0 Tos:0 Table:52 Protocol:0 Scope:0 Type:1 Flags:0 Attributes:{Dst:<nil> Src:<nil> Gateway:<nil> OutIface:0 Priority:5270 Table:52 Mark:0 Pref:<nil> Expires:<nil> Metrics:<nil> Multipath:[]}}\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.583943169Z","proc_id":**********,"proc_seq":41},"text":"monitor: [unexpected] network state changed, but stringification didn't: interfaces.State{defaultRoute=eth0 ifs={br-1c0fa1572040:[**********/16 llu6] br-ab00c5797122:[**********/16 llu6] br-ac85f49a1a43:[**********/16] br-d3f50c987d01:[**********/16 llu6] docker0:[**********/16] eth0:[************/24 fdc4:f303:9324::3/64 llu6] services1:[************/32 fdc4:f303:9324::6/128 llu6]} v4=true v6=true}\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.584265179Z","proc_id":**********,"proc_seq":42},"text":"monitor: [unexpected] old: {\"InterfaceIPs\":{\"br-1c0fa1572040\":[\"**********/16\",\"fe80::408e:7fff:fee4:604f/64\"],\"br-ab00c5797122\":[\"**********/16\",\"fe80::20a3:6eff:fe47:a10f/64\"],\"br-ac85f49a1a43\":[\"**********/16\"],\"br-d3f50c987d01\":[\"**********/16\",\"fe80::a4da:f5ff:fe4b:2d58/64\"],\"docker0\":[\"**********/16\"],\"eth0\":[\"************/24\",\"fdc4:f303:9324::3/64\",\"fe80::90f8:43ff:fe77:1b73/64\"],\"lo\":[\"127.0.0.1/8\",\"::1/128\"],\"services1\":[\"************/32\",\"fdc4:f303:9324::6/128\",\"fe80::4db:91ff:fe22:17c5/64\"],\"veth0799f3a\":[\"fe80::e053:f0ff:fe43:a0ab/64\"],\"veth08bd244\":[\"fe80::ecc5:19ff:fe2e:a8c/64\"],\"veth2ba53ed\":[\"fe80::ec77:a7ff:feb2:5196/64\"],\"veth5a860f0\":[\"fe80::ecce:4ff:fe8a:e847/64\"],\"veth5e7e4e3\":[\"fe80::e4c2:b4ff:fe61:85fc/64\"],\"veth6b9794e\":[\"fe80::b0ee:a0ff:fe74:29be/64\"],\"veth8d7757c\":[\"fe80::1ce9:39ff:fee8:f692/64\"],\"vethba71ec6\":[\"fe80::d8d4:2aff:fe74:6d91/64\"],\"vethdbc7636\":[\"fe80::2872:9aff:fe22:66b5/64\"],\"vethe1bf190\":[\"fe80::2010:21ff:fe9c:57b4/64\"],\"vethe3e163f\":[\"fe80::4037:5aff:fe8c:161f/64\"],\"vethecaa3db\":[\"fe80::4c9e:35ff:fef7:e23f/64\"],\"vetheec96c3\":[\"fe80::acb3:8dff:fe73:b658/64\"]},\"Interface\":{\"br-1c0fa1572040\":{\"Index\":4,\"MTU\":1500,\"Name\":\"br-1c0fa1572040\",\"HardwareAddr\":\"Qo5/5GBP\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"br-ab00c5797122\":{\"Index\":6,\"MTU\":1500,\"Name\":\"br-ab00c5797122\",\"HardwareAddr\":\"IqNuR6EP\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"br-ac85f49a1a43\":{\"Index\":7,\"MTU\":1500,\"Name\":\"br-ac85f49a1a43\",\"HardwareAddr\":\"atZZmp4b\",\"Flags\":19,\"AltAddrs\":null,\"Desc\":\"\"},\"br-d3f50c987d01\":{\"Index\":8,\"MTU\":1500,\"Name\":\"br-d3f50c987d01\",\"HardwareAddr\":\"ptr1Sy1Y\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"docker0\":{\"Index\":5,\"MTU\":1500,\"Name\":\"docker0\",\"HardwareAddr\":\"so2JDNZ5\",\"Flags\":19,\"AltAddrs\":null,\"Desc\":\"\"},\"eth0\":{\"Index\":3,\"MTU\":1500,\"Name\":\"eth0\",\"HardwareAddr\":\"kvhDdxtz\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"lo\":{\"Index\":1,\"MTU\":65536,\"Name\":\"lo\",\"HardwareAddr\":null,\"Flags\":37,\"AltAddrs\":null,\"Desc\":\"\"},\"services1\":{\"Index\":2,\"MTU\":1500,\"Name\":\"services1\",\"HardwareAddr\":\"BtuRIhfF\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"veth0799f3a\":{\"Index\":3208,\"MTU\":1500,\"Name\":\"veth0799f3a\",\"HardwareAddr\":\"4lPwQ6Cr\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"veth08bd244\":{\"Index\":3126,\"MTU\":1500,\"Name\":\"veth08bd244\",\"HardwareAddr\":\"7sUZLgqM\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"veth2ba53ed\":{\"Index\":3194,\"MTU\":1500,\"Name\":\"veth2ba53ed\",\"HardwareAddr\":\"7nenslGW\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"veth5a860f0\":{\"Index\":3239,\"MTU\":1500,\"Name\":\"veth5a860f0\",\"HardwareAddr\":\"7s4EiuhH\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"veth5e7e4e3\":{\"Index\":3125,\"MTU\":1500,\"Name\":\"veth5e7e4e3\",\"HardwareAddr\":\"5sK0YYX8\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"veth6b9794e\":{\"Index\":3136,\"MTU\":1500,\"Name\":\"veth6b9794e\",\"HardwareAddr\":\"su6gdCm+\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"veth8d7757c\":{\"Index\":3122,\"MTU\":1500,\"Name\":\"veth8d7757c\",\"HardwareAddr\":\"Huk56PaS\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"vethba71ec6\":{\"Index\":3207,\"MTU\":1500,\"Name\":\"vethba71ec6\",\"HardwareAddr\":\"2tQqdG2R\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"vethdbc7636\":{\"Index\":3132,\"MTU\":1500,\"Name\":\"vethdbc7636\",\"HardwareAddr\":\"KnKaIma1\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"vethe1bf190\":{\"Index\":3124,\"MTU\":1500,\"Name\":\"vethe1bf190\",\"HardwareAddr\":\"IhAhnFe0\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"vethe3e163f\":{\"Index\":3121,\"MTU\":1500,\"Name\":\"vethe3e163f\",\"HardwareAddr\":\"QjdajBYf\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"vethecaa3db\":{\"Index\":9,\"MTU\":1500,\"Name\":\"vethecaa3db\",\"HardwareAddr\":\"Tp419+I/\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"vetheec96c3\":{\"Index\":3127,\"MTU\":1500,\"Name\":\"vetheec96c3\",\"HardwareAddr\":\"rrONc7ZY\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"}},\"HaveV6\":true,\"HaveV4\":true,\"IsExpensive\":false,\"DefaultRouteInterface\":\"eth0\",\"HTTPProxy\":\"\",\"PAC\":\"\"}\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.584474092Z","proc_id":**********,"proc_seq":43},"text":"monitor: [unexpected] new: {\"InterfaceIPs\":{\"br-1c0fa1572040\":[\"**********/16\",\"fe80::408e:7fff:fee4:604f/64\"],\"br-ab00c5797122\":[\"**********/16\",\"fe80::20a3:6eff:fe47:a10f/64\"],\"br-ac85f49a1a43\":[\"**********/16\"],\"br-d3f50c987d01\":[\"**********/16\",\"fe80::a4da:f5ff:fe4b:2d58/64\"],\"docker0\":[\"**********/16\"],\"eth0\":[\"************/24\",\"fdc4:f303:9324::3/64\",\"fe80::90f8:43ff:fe77:1b73/64\"],\"lo\":[\"127.0.0.1/8\",\"::1/128\"],\"services1\":[\"************/32\",\"fdc4:f303:9324::6/128\",\"fe80::4db:91ff:fe22:17c5/64\"],\"tailscale0\":[\"fe80::e1:a219:88b1:e7db/64\"],\"veth0799f3a\":[\"fe80::e053:f0ff:fe43:a0ab/64\"],\"veth08bd244\":[\"fe80::ecc5:19ff:fe2e:a8c/64\"],\"veth2ba53ed\":[\"fe80::ec77:a7ff:feb2:5196/64\"],\"veth5a860f0\":[\"fe80::ecce:4ff:fe8a:e847/64\"],\"veth5e7e4e3\":[\"fe80::e4c2:b4ff:fe61:85fc/64\"],\"veth6b9794e\":[\"fe80::b0ee:a0ff:fe74:29be/64\"],\"veth8d7757c\":[\"fe80::1ce9:39ff:fee8:f692/64\"],\"vethba71ec6\":[\"fe80::d8d4:2aff:fe74:6d91/64\"],\"vethdbc7636\":[\"fe80::2872:9aff:fe22:66b5/64\"],\"vethe1bf190\":[\"fe80::2010:21ff:fe9c:57b4/64\"],\"vethe3e163f\":[\"fe80::4037:5aff:fe8c:161f/64\"],\"vethecaa3db\":[\"fe80::4c9e:35ff:fef7:e23f/64\"],\"vetheec96c3\":[\"fe80::acb3:8dff:fe73:b658/64\"]},\"Interface\":{\"br-1c0fa1572040\":{\"Index\":4,\"MTU\":1500,\"Name\":\"br-1c0fa1572040\",\"HardwareAddr\":\"Qo5/5GBP\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"br-ab00c5797122\":{\"Index\":6,\"MTU\":1500,\"Name\":\"br-ab00c5797122\",\"HardwareAddr\":\"IqNuR6EP\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"br-ac85f49a1a43\":{\"Index\":7,\"MTU\":1500,\"Name\":\"br-ac85f49a1a43\",\"HardwareAddr\":\"atZZmp4b\",\"Flags\":19,\"AltAddrs\":null,\"Desc\":\"\"},\"br-d3f50c987d01\":{\"Index\":8,\"MTU\":1500,\"Name\":\"br-d3f50c987d01\",\"HardwareAddr\":\"ptr1Sy1Y\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"docker0\":{\"Index\":5,\"MTU\":1500,\"Name\":\"docker0\",\"HardwareAddr\":\"so2JDNZ5\",\"Flags\":19,\"AltAddrs\":null,\"Desc\":\"\"},\"eth0\":{\"Index\":3,\"MTU\":1500,\"Name\":\"eth0\",\"HardwareAddr\":\"kvhDdxtz\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"lo\":{\"Index\":1,\"MTU\":65536,\"Name\":\"lo\",\"HardwareAddr\":null,\"Flags\":37,\"AltAddrs\":null,\"Desc\":\"\"},\"services1\":{\"Index\":2,\"MTU\":1500,\"Name\":\"services1\",\"HardwareAddr\":\"BtuRIhfF\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"tailscale0\":{\"Index\":3246,\"MTU\":1280,\"Name\":\"tailscale0\",\"HardwareAddr\":null,\"Flags\":57,\"AltAddrs\":null,\"Desc\":\"\"},\"veth0799f3a\":{\"Index\":3208,\"MTU\":1500,\"Name\":\"veth0799f3a\",\"HardwareAddr\":\"4lPwQ6Cr\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"veth08bd244\":{\"Index\":3126,\"MTU\":1500,\"Name\":\"veth08bd244\",\"HardwareAddr\":\"7sUZLgqM\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"veth2ba53ed\":{\"Index\":3194,\"MTU\":1500,\"Name\":\"veth2ba53ed\",\"HardwareAddr\":\"7nenslGW\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"veth5a860f0\":{\"Index\":3239,\"MTU\":1500,\"Name\":\"veth5a860f0\",\"HardwareAddr\":\"7s4EiuhH\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"veth5e7e4e3\":{\"Index\":3125,\"MTU\":1500,\"Name\":\"veth5e7e4e3\",\"HardwareAddr\":\"5sK0YYX8\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"veth6b9794e\":{\"Index\":3136,\"MTU\":1500,\"Name\":\"veth6b9794e\",\"HardwareAddr\":\"su6gdCm+\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"veth8d7757c\":{\"Index\":3122,\"MTU\":1500,\"Name\":\"veth8d7757c\",\"HardwareAddr\":\"Huk56PaS\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"vethba71ec6\":{\"Index\":3207,\"MTU\":1500,\"Name\":\"vethba71ec6\",\"HardwareAddr\":\"2tQqdG2R\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"vethdbc7636\":{\"Index\":3132,\"MTU\":1500,\"Name\":\"vethdbc7636\",\"HardwareAddr\":\"KnKaIma1\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"vethe1bf190\":{\"Index\":3124,\"MTU\":1500,\"Name\":\"vethe1bf190\",\"HardwareAddr\":\"IhAhnFe0\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"vethe3e163f\":{\"Index\":3121,\"MTU\":1500,\"Name\":\"vethe3e163f\",\"HardwareAddr\":\"QjdajBYf\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"vethecaa3db\":{\"Index\":9,\"MTU\":1500,\"Name\":\"vethecaa3db\",\"HardwareAddr\":\"Tp419+I/\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"},\"vetheec96c3\":{\"Index\":3127,\"MTU\":1500,\"Name\":\"vetheec96c3\",\"HardwareAddr\":\"rrONc7ZY\",\"Flags\":51,\"AltAddrs\":null,\"Desc\":\"\"}},\"HaveV6\":true,\"HaveV4\":true,\"IsExpensive\":false,\"DefaultRouteInterface\":\"eth0\",\"HTTPProxy\":\"\",\"PAC\":\"\"}\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.58473936Z","proc_id":**********,"proc_seq":44},"text":"LinkChange: major, rebinding. New state: interfaces.State{defaultRoute=eth0 ifs={br-1c0fa1572040:[**********/16 llu6] br-ab00c5797122:[**********/16 llu6] br-ac85f49a1a43:[**********/16] br-d3f50c987d01:[**********/16 llu6] docker0:[**********/16] eth0:[************/24 fdc4:f303:9324::3/64 llu6] services1:[************/32 fdc4:f303:9324::6/128 llu6]} v4=true v6=true}\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.585421752Z","proc_id":**********,"proc_seq":45},"text":"onPortUpdate(port=34724, network=udp6)\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.585642576Z","proc_id":**********,"proc_seq":46},"text":"pm: using backend prefs for \"profile-d36f\": Prefs{ra=false dns=false want=true routes=[**********/16 **********/16] snat=true statefulFiltering=false nf=on update=check Persist{o=, n=[6RWcy] u=\"<EMAIL>\"}}\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.585955946Z","proc_id":**********,"proc_seq":47},"text":"onPortUpdate(port=55619, network=udp4)\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.586131453Z","proc_id":**********,"proc_seq":48},"v":1,"text":"magicsock: peermtu: peer MTU status is false\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.58630117Z","proc_id":**********,"proc_seq":49},"text":"Rebind; defIf=\"eth0\", ips=[************/24 fdc4:f303:9324::3/64 fe80::90f8:43ff:fe77:1b73/64]\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.58647436Z","proc_id":**********,"proc_seq":50},"text":"magicsock: 0 active derp conns\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.586947732Z","proc_id":**********,"proc_seq":51},"text":"logpolicy: using system state directory \"/var/lib/tailscale\"\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.587840648Z","proc_id":**********,"proc_seq":52},"text":"monitor: gateway and self IP changed: gw=************ self=************\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.588095262Z","proc_id":**********,"proc_seq":53},"v":1,"text":"netmap packet filter: (not ready yet)\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.590254383Z","proc_id":**********,"proc_seq":54},"v":2,"text":"dnsfallback: SetCachePath loaded cached DERP map\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.590711496Z","proc_id":**********,"proc_seq":55},"text":"got LocalBackend in 29ms\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.590863338Z","proc_id":**********,"proc_seq":56},"text":"Start\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.592447835Z","proc_id":**********,"proc_seq":57},"text":"ipnext: active extensions: relayserver, taildrop\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.593011287Z","proc_id":**********,"proc_seq":58},"v":1,"Hostinfo":{"IPNVersion":"1.84.3-t7648989bc","BackendLogID":"273e454a9471b565e078062ac7e0d04bc247e138030736cd6712d0a453341148","OS":"linux","OSVersion":"**********-microsoft-standard-WSL2","Container":true,"Distro":"alpine","DistroVersion":"3.19.7","Desktop":false,"Package":"container","Hostname":"abacus-server","Machine":"x86_64","GoArch":"amd64","GoArchVar":"v1","GoVersion":"go1.24.2","Userspace":false,"UserspaceRouter":false,"AppConnector":false}}
{"logtail":{"client_time":"2025-07-17T17:41:37.593237462Z","proc_id":**********,"proc_seq":59},"v":1,"text":"control: HostInfo: {\"IPNVersion\":\"1.84.3-t7648989bc\",\"BackendLogID\":\"273e454a9471b565e078062ac7e0d04bc247e138030736cd6712d0a453341148\",\"OS\":\"linux\",\"OSVersion\":\"**********-microsoft-standard-WSL2\",\"Container\":true,\"Distro\":\"alpine\",\"DistroVersion\":\"3.19.7\",\"Desktop\":false,\"Package\":\"container\",\"Hostname\":\"abacus-server\",\"Machine\":\"x86_64\",\"GoArch\":\"amd64\",\"GoArchVar\":\"v1\",\"GoVersion\":\"go1.24.2\",\"RoutableIPs\":[\"**********/16\",\"**********/16\"],\"Userspace\":false,\"UserspaceRouter\":false,\"AppConnector\":false}\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.593451535Z","proc_id":**********,"proc_seq":60},"v":1,"text":"control: authRoutine: state:new; goal=nil paused=false\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.593624889Z","proc_id":**********,"proc_seq":61},"v":1,"text":"control: mapRoutine: state:new\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.593799078Z","proc_id":**********,"proc_seq":62},"text":"Backend: logs: be:273e454a9471b565e078062ac7e0d04bc247e138030736cd6712d0a453341148 fe:\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.596171387Z","proc_id":**********,"proc_seq":63},"text":"control: client.Login(0)\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.596335943Z","proc_id":**********,"proc_seq":64},"v":1,"text":"control: mapRoutine: context done.\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.596482384Z","proc_id":**********,"proc_seq":65},"v":1,"text":"control: mapRoutine: state:new\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.596599591Z","proc_id":**********,"proc_seq":66},"v":1,"text":"control: authRoutine: context done.\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.596719567Z","proc_id":**********,"proc_seq":67},"v":1,"text":"control: authRoutine: state:new; wantLoggedIn=true\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.596862157Z","proc_id":**********,"proc_seq":68},"v":1,"text":"control: direct.TryLogin(flags=0)\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.597057805Z","proc_id":**********,"proc_seq":69},"text":"control: doLogin(regen=false, hasUrl=false)\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.597275807Z","proc_id":**********,"proc_seq":70},"text":"health(warnable=warming-up): error: Tailscale is starting. Please wait.\n"}
{"logtail":{"client_time":"2025-07-17T17:41:37.783304125Z","proc_id":**********,"proc_seq":71},"text":"tailscaled got signal terminated; shutting down\n"}
