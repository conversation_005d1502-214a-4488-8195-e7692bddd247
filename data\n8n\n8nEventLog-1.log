{"__type":"$$EventMessageWorkflow","id":"049596a1-cfab-4101-afc9-e09933cafb65","ts":"2025-07-17T13:59:53.794-04:00","eventName":"n8n.workflow.started","message":"n8n.workflow.started","payload":{"executionId":"8103","workflowId":"7I3EfxpsDgrByI8X","isManual":false,"workflowName":"Email Labler"}}
{"__type":"$$EventMessageConfirm","confirm":"049596a1-cfab-4101-afc9-e09933cafb65","ts":"2025-07-17T13:59:53.795-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"b506ff8a-8956-49ee-b0ea-057c8f0d9618","ts":"2025-07-17T13:59:53.795-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8103","nodeType":"n8n-nodes-base.gmailTrigger","nodeName":"Gmail Trigger","nodeId":"6738eab9-dd67-4bb1-b0d7-d376f6c65fce"}}
{"__type":"$$EventMessageConfirm","confirm":"b506ff8a-8956-49ee-b0ea-057c8f0d9618","ts":"2025-07-17T13:59:53.795-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"2d80ca89-c2e0-4bcf-9981-cf00d36b8026","ts":"2025-07-17T13:59:53.796-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8103","nodeType":"n8n-nodes-base.gmailTrigger","nodeName":"Gmail Trigger","nodeId":"6738eab9-dd67-4bb1-b0d7-d376f6c65fce"}}
{"__type":"$$EventMessageConfirm","confirm":"2d80ca89-c2e0-4bcf-9981-cf00d36b8026","ts":"2025-07-17T13:59:53.796-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"9996e593-e595-4594-852c-b8aeb3b35efc","ts":"2025-07-17T13:59:53.796-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8103","nodeType":"@n8n/n8n-nodes-langchain.agent","nodeName":"AI Agent","nodeId":"fa2efd94-444c-4efc-8f00-d76b47673a95"}}
{"__type":"$$EventMessageConfirm","confirm":"9996e593-e595-4594-852c-b8aeb3b35efc","ts":"2025-07-17T13:59:53.796-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"1f46b63c-0807-48d7-8723-f5ac851d03e5","ts":"2025-07-17T13:59:55.620-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8103","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","nodeName":"Google Gemini Chat Model","nodeId":"2431cbe7-32f6-4b11-a623-da84241e6dd2"}}
{"__type":"$$EventMessageConfirm","confirm":"1f46b63c-0807-48d7-8723-f5ac851d03e5","ts":"2025-07-17T13:59:55.620-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"8a23b02b-d463-430d-8350-03a5b6e6977a","ts":"2025-07-17T13:59:57.685-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8103","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","nodeName":"Google Gemini Chat Model","nodeId":"2431cbe7-32f6-4b11-a623-da84241e6dd2"}}
{"__type":"$$EventMessageConfirm","confirm":"8a23b02b-d463-430d-8350-03a5b6e6977a","ts":"2025-07-17T13:59:57.685-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageAiNode","id":"966652bb-5f15-403e-9e82-84eeacbe5932","ts":"2025-07-17T13:59:57.686-04:00","eventName":"n8n.ai.llm.generated","message":"n8n.ai.llm.generated","payload":{"executionId":"8103","nodeName":"Google Gemini Chat Model","workflowName":"Email Labler","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","workflowId":"7I3EfxpsDgrByI8X","msg":"{\"messages\":[\"System: You are an AI Email Classification Agent operating within a system that provides access to specific Gmail tools (gmail_list_label, gmail_add_label) via a model context protocol server. Your task is to:\\nDetermine the correct existing Label ID for an incoming email based solely on the definitive list of labels provided below.\\nExecute the labeling by calling the gmail_add_label tool with the determined Label ID and the email's Message ID.\\nInputs:\\nIncoming Email (Sender, Subject, Body)\\nEmail Message ID (Let's assume this is available to you as message_id_variable)\\nProcedural Step: Although the definitive list of labels is provided below, you may need to invoke the gmail_list_label tool as the initial step if required by the system framework. However, for determining the correct label ID, you must strictly adhere to the names and IDs listed in the \\\"Definitive Label List\\\" section below.\\nDefinitive Label List (Source of Truth for Names and IDs):\\nCHAT / CHAT (System Label - Ignore)\\nSENT / SENT (System Label - Ignore)\\nINBOX / INBOX (System Label - Ignore)\\nIMPORTANT / IMPORTANT (System Label - Use ID IMPORTANT for the 'Important' conceptual category)\\nTRASH / TRASH (System Label - Ignore)\\nDRAFT / DRAFT (System Label - Ignore)\\nSPAM / SPAM (System Label - Ignore)\\n... (other system/category labels to ignore for classification) ...\\nInbox Zero / Label_12 (User Label - Ignore)\\nInbox Zero/Acted / Label_13 (User Label - Ignore)\\nUnimportant / Label_14 (User Label - Use for 'Unimportant' category)\\nVerification Codes / Label_15 (User Label - Use for 'Verification Codes' category)\\nApplication Status / Label_16 (User Label - Use for 'Application Status' category)\\nMeeting Confirmed / Label_17 (User Label - Use for 'Meeting Confirmed' category)\\nMessenger Notifications / Label_18 (User Label - Use for 'Messenger Notifications' category)\\nRocket Money Updates / Label_19 (User Label - Use for 'Rocket Money Updates' category)\\nApple Music Analytics / Label_20 (User Label - Use for 'Apple Music Analytics' category)\\nJob Suggestions / Label_21 (User Label - Use for 'Job Suggestions' category)\\nAi-Agent / Label_2878194751042528826 (User Label - Ignore)\\nYoutube Comments / Label_3070697212981345438 (User Label - Use for 'Youtube Comments' category)\\nAi-Agent/Checked / Label_6685525344120202453 (User Label - Ignore)\\nUber Receipts / Label_7198370523552713631 (User Label - Use for 'Uber Receipts' category)\\nAi-Agent/Checked/Unsure / Label_7805829971573720853 (User Label - Ignore)\\n(This list represents all labels currently recognized for classification based on the rules below).\\nClassification and Execution Process:\\n(Procedural Step - If required): Call gmail_list_label.\\nAnalyze Input: Examine the incoming email (Sender, Subject, Body) and note the message_id_variable.\\nMatch Criteria: Compare the email content against the Criteria for the Conceptual Label Names listed below. Apply rules and exclusions strictly.\\nSelect Category: Determine the single most appropriate Conceptual Label Name.\\nIdentify Designated ID: Find the selected Conceptual Label Name in the rules section below and identify its Designated Label ID based on the Definitive Label List above.\\nFallback Logic:\\nIf the email doesn't fit any specific category (1, 2, 5-11), use the 'Important' fallback (Designated ID: IMPORTANT).\\nIf it doesn't fit any specific category or 'Important', and doesn't hit 'Unimportant' exclusions, use the 'Unimportant' fallback (Designated ID: Label_14).\\nIf no category applies even after fallbacks, stop and indicate failure (NO_CATEGORY_MATCH).\\nPrepare Tool Call: You have now determined the target_label_id (the Designated Label ID from step 5 or 6).\\nExecute Labeling Action: Call the gmail_add_label tool. You MUST provide the following parameters:\\nlabel_id: The target_label_id you determined.\\nmessage_id: The message_id_variable provided in the input.\\nFinal Output: Report the outcome of the tool call. For example: \\\"Successfully called gmail_add_label to apply Label ID '[target_label_id]' to message '[message_id_variable]'.\\\" or \\\"Failed to apply label: [Reason from tool call]\\\". Do not just output the Label ID.\\nConceptual Label Names, Criteria, and Designated IDs:\\n(Match email to criteria. Use the corresponding Designated Label ID from the Definitive List above when calling gmail_add_label.)\\nConceptual Label Name: Application Status -> Designated Label ID: Label_16\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Job Suggestions -> Designated Label ID: Label_21\\nCriteria: ... [criteria & exclusions] ...\\nConceptual Label Name: Important -> Designated Label ID: IMPORTANT\\nCriteria: ... [criteria - fallback 1] ...\\nConceptual Label Name: Unimportant -> Designated Label ID: Label_14\\nCriteria: ... [criteria & exclusions - fallback 2] ...\\nConceptual Label Name: Verification Codes -> Designated Label ID: Label_15\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Meeting Confirmed -> Designated Label ID: Label_17\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Apple Music Analytics -> Designated Label ID: Label_20\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Rocket Money Updates -> Designated Label ID: Label_19\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Messenger Notifications -> Designated Label ID: Label_18\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Youtube Comments -> Designated Label ID: Label_3070697212981345438\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Uber Receipts -> Designated Label ID: Label_7198370523552713631\\nCriteria: ... [criteria] ...\\nExample Agent Execution Flow:\\nInput: Email (Unimportant offer), message_id_variable = \\\"msg_abc123\\\"\\nAI Reasoning (Internal):\\n(Calls gmail_list_label if needed).\\nAnalyzes email content -> Matches 'Unimportant'.\\nLooks up 'Unimportant' -> Designated ID is Label_14.\\nPrepare tool call: label_id='Label_14', message_id='msg_abc123'.\\nAI Action: Calls gmail_add_label(label_id='Label_14', message_id='msg_abc123').\\n(Assume Tool Call Succeeds)\\nAI Final Output: \\\"Successfully called gmail_add_label to apply Label ID 'Label_14' to message 'msg_abc123'.\\\"\\nHuman: ID: 198198ab515315e7\\nFrom: Indeed <<EMAIL>>\\nSubject: Host @ Areas USA, Inc\\nBody: $20.10 an hour. Hi jalen, It looks like your background could be a match for this Host role. Please submit a quick application if you have any interest. ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌\"],\"options\":{\"google_api_key\":{\"lc\":1,\"type\":\"secret\",\"id\":[\"GOOGLE_API_KEY\"]},\"base_url\":\"https://generativelanguage.googleapis.com\",\"model\":\"gemini-2.5-flash-preview-05-20\"},\"response\":{\"response\":{\"generations\":[[{\"text\":\"\",\"generationInfo\":{\"finishReason\":\"STOP\",\"index\":0}}]]},\"tokenUsage\":{\"completionTokens\":45,\"promptTokens\":1821,\"totalTokens\":1866}}}"}}
{"__type":"$$EventMessageConfirm","confirm":"966652bb-5f15-403e-9e82-84eeacbe5932","ts":"2025-07-17T13:59:57.686-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"31fd993c-bd63-4155-97ea-132668036b58","ts":"2025-07-17T13:59:57.694-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8103","nodeType":"@n8n/n8n-nodes-langchain.mcpClientTool","nodeName":"Gmail Tools","nodeId":"1bf6d8f2-728a-43e7-b1c0-f35da20d0b76"}}
{"__type":"$$EventMessageConfirm","confirm":"31fd993c-bd63-4155-97ea-132668036b58","ts":"2025-07-17T13:59:57.695-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageAiNode","id":"4856b116-55b2-4907-b041-31aa33c6df7f","ts":"2025-07-17T13:59:58.131-04:00","eventName":"n8n.ai.tool.called","message":"n8n.ai.tool.called","payload":{"executionId":"8103","nodeName":"Gmail Tools","workflowName":"Email Labler","nodeType":"@n8n/n8n-nodes-langchain.mcpClientTool","workflowId":"7I3EfxpsDgrByI8X","msg":"{\"query\":{\"Message_ID\":\"198198ab515315e7\",\"Label_Names_or_IDs\":\"Label_21\"},\"tool\":{\"name\":\"Add_Label\",\"description\":\"Add label to message in Gmail\"},\"response\":[{\"type\":\"text\",\"text\":\"[{\\\"id\\\":\\\"198198ab515315e7\\\",\\\"threadId\\\":\\\"198198ab515315e7\\\",\\\"labelIds\\\":[\\\"UNREAD\\\",\\\"Label_21\\\",\\\"CATEGORY_UPDATES\\\",\\\"INBOX\\\"]}]\"}]}"}}
{"__type":"$$EventMessageConfirm","confirm":"4856b116-55b2-4907-b041-31aa33c6df7f","ts":"2025-07-17T13:59:58.131-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"2d31c1bb-e042-4410-a519-c85f649fb8a5","ts":"2025-07-17T13:59:58.131-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8103","nodeType":"@n8n/n8n-nodes-langchain.mcpClientTool","nodeName":"Gmail Tools","nodeId":"1bf6d8f2-728a-43e7-b1c0-f35da20d0b76"}}
{"__type":"$$EventMessageConfirm","confirm":"2d31c1bb-e042-4410-a519-c85f649fb8a5","ts":"2025-07-17T13:59:58.131-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"1fc1b58f-351e-4487-ba87-ab2f9db1e38e","ts":"2025-07-17T13:59:58.145-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8103","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","nodeName":"Google Gemini Chat Model","nodeId":"2431cbe7-32f6-4b11-a623-da84241e6dd2"}}
{"__type":"$$EventMessageConfirm","confirm":"1fc1b58f-351e-4487-ba87-ab2f9db1e38e","ts":"2025-07-17T13:59:58.145-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageWorkflow","id":"edfa666b-9c63-4622-8f22-ec2204f9b662","ts":"2025-07-17T13:59:58.222-04:00","eventName":"n8n.workflow.started","message":"n8n.workflow.started","payload":{"executionId":"8104","workflowId":"7I3EfxpsDgrByI8X","isManual":false,"workflowName":"Email Labler"}}
{"__type":"$$EventMessageConfirm","confirm":"edfa666b-9c63-4622-8f22-ec2204f9b662","ts":"2025-07-17T13:59:58.222-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"58a1e29e-da8d-4d38-a9e4-2d536caa6245","ts":"2025-07-17T13:59:58.222-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8104","nodeType":"@n8n/n8n-nodes-langchain.mcpTrigger","nodeName":"MCP Server Trigger","nodeId":"738db1fc-738d-4d2b-9778-697d652bf9d3"}}
{"__type":"$$EventMessageConfirm","confirm":"58a1e29e-da8d-4d38-a9e4-2d536caa6245","ts":"2025-07-17T13:59:58.222-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"11ccaf05-14d9-453d-af11-144d927c5073","ts":"2025-07-17T13:59:58.222-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8104","nodeType":"@n8n/n8n-nodes-langchain.mcpTrigger","nodeName":"MCP Server Trigger","nodeId":"738db1fc-738d-4d2b-9778-697d652bf9d3"}}
{"__type":"$$EventMessageConfirm","confirm":"11ccaf05-14d9-453d-af11-144d927c5073","ts":"2025-07-17T13:59:58.222-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageWorkflow","id":"b2695e26-ea4b-4272-bc09-017622b31669","ts":"2025-07-17T13:59:58.223-04:00","eventName":"n8n.workflow.success","message":"n8n.workflow.success","payload":{"executionId":"8104","success":true,"isManual":false,"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler"}}
{"__type":"$$EventMessageConfirm","confirm":"b2695e26-ea4b-4272-bc09-017622b31669","ts":"2025-07-17T13:59:58.223-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"d19b85bc-5cca-425f-a6d8-0ab5c4b865d7","ts":"2025-07-17T13:59:59.758-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8103","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","nodeName":"Google Gemini Chat Model","nodeId":"2431cbe7-32f6-4b11-a623-da84241e6dd2"}}
{"__type":"$$EventMessageConfirm","confirm":"d19b85bc-5cca-425f-a6d8-0ab5c4b865d7","ts":"2025-07-17T13:59:59.758-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageAiNode","id":"183dab73-d74a-4e27-a9d9-3bc77c5270e8","ts":"2025-07-17T13:59:59.758-04:00","eventName":"n8n.ai.llm.generated","message":"n8n.ai.llm.generated","payload":{"executionId":"8103","nodeName":"Google Gemini Chat Model","workflowName":"Email Labler","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","workflowId":"7I3EfxpsDgrByI8X","msg":"{\"messages\":[\"System: You are an AI Email Classification Agent operating within a system that provides access to specific Gmail tools (gmail_list_label, gmail_add_label) via a model context protocol server. Your task is to:\\nDetermine the correct existing Label ID for an incoming email based solely on the definitive list of labels provided below.\\nExecute the labeling by calling the gmail_add_label tool with the determined Label ID and the email's Message ID.\\nInputs:\\nIncoming Email (Sender, Subject, Body)\\nEmail Message ID (Let's assume this is available to you as message_id_variable)\\nProcedural Step: Although the definitive list of labels is provided below, you may need to invoke the gmail_list_label tool as the initial step if required by the system framework. However, for determining the correct label ID, you must strictly adhere to the names and IDs listed in the \\\"Definitive Label List\\\" section below.\\nDefinitive Label List (Source of Truth for Names and IDs):\\nCHAT / CHAT (System Label - Ignore)\\nSENT / SENT (System Label - Ignore)\\nINBOX / INBOX (System Label - Ignore)\\nIMPORTANT / IMPORTANT (System Label - Use ID IMPORTANT for the 'Important' conceptual category)\\nTRASH / TRASH (System Label - Ignore)\\nDRAFT / DRAFT (System Label - Ignore)\\nSPAM / SPAM (System Label - Ignore)\\n... (other system/category labels to ignore for classification) ...\\nInbox Zero / Label_12 (User Label - Ignore)\\nInbox Zero/Acted / Label_13 (User Label - Ignore)\\nUnimportant / Label_14 (User Label - Use for 'Unimportant' category)\\nVerification Codes / Label_15 (User Label - Use for 'Verification Codes' category)\\nApplication Status / Label_16 (User Label - Use for 'Application Status' category)\\nMeeting Confirmed / Label_17 (User Label - Use for 'Meeting Confirmed' category)\\nMessenger Notifications / Label_18 (User Label - Use for 'Messenger Notifications' category)\\nRocket Money Updates / Label_19 (User Label - Use for 'Rocket Money Updates' category)\\nApple Music Analytics / Label_20 (User Label - Use for 'Apple Music Analytics' category)\\nJob Suggestions / Label_21 (User Label - Use for 'Job Suggestions' category)\\nAi-Agent / Label_2878194751042528826 (User Label - Ignore)\\nYoutube Comments / Label_3070697212981345438 (User Label - Use for 'Youtube Comments' category)\\nAi-Agent/Checked / Label_6685525344120202453 (User Label - Ignore)\\nUber Receipts / Label_7198370523552713631 (User Label - Use for 'Uber Receipts' category)\\nAi-Agent/Checked/Unsure / Label_7805829971573720853 (User Label - Ignore)\\n(This list represents all labels currently recognized for classification based on the rules below).\\nClassification and Execution Process:\\n(Procedural Step - If required): Call gmail_list_label.\\nAnalyze Input: Examine the incoming email (Sender, Subject, Body) and note the message_id_variable.\\nMatch Criteria: Compare the email content against the Criteria for the Conceptual Label Names listed below. Apply rules and exclusions strictly.\\nSelect Category: Determine the single most appropriate Conceptual Label Name.\\nIdentify Designated ID: Find the selected Conceptual Label Name in the rules section below and identify its Designated Label ID based on the Definitive Label List above.\\nFallback Logic:\\nIf the email doesn't fit any specific category (1, 2, 5-11), use the 'Important' fallback (Designated ID: IMPORTANT).\\nIf it doesn't fit any specific category or 'Important', and doesn't hit 'Unimportant' exclusions, use the 'Unimportant' fallback (Designated ID: Label_14).\\nIf no category applies even after fallbacks, stop and indicate failure (NO_CATEGORY_MATCH).\\nPrepare Tool Call: You have now determined the target_label_id (the Designated Label ID from step 5 or 6).\\nExecute Labeling Action: Call the gmail_add_label tool. You MUST provide the following parameters:\\nlabel_id: The target_label_id you determined.\\nmessage_id: The message_id_variable provided in the input.\\nFinal Output: Report the outcome of the tool call. For example: \\\"Successfully called gmail_add_label to apply Label ID '[target_label_id]' to message '[message_id_variable]'.\\\" or \\\"Failed to apply label: [Reason from tool call]\\\". Do not just output the Label ID.\\nConceptual Label Names, Criteria, and Designated IDs:\\n(Match email to criteria. Use the corresponding Designated Label ID from the Definitive List above when calling gmail_add_label.)\\nConceptual Label Name: Application Status -> Designated Label ID: Label_16\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Job Suggestions -> Designated Label ID: Label_21\\nCriteria: ... [criteria & exclusions] ...\\nConceptual Label Name: Important -> Designated Label ID: IMPORTANT\\nCriteria: ... [criteria - fallback 1] ...\\nConceptual Label Name: Unimportant -> Designated Label ID: Label_14\\nCriteria: ... [criteria & exclusions - fallback 2] ...\\nConceptual Label Name: Verification Codes -> Designated Label ID: Label_15\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Meeting Confirmed -> Designated Label ID: Label_17\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Apple Music Analytics -> Designated Label ID: Label_20\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Rocket Money Updates -> Designated Label ID: Label_19\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Messenger Notifications -> Designated Label ID: Label_18\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Youtube Comments -> Designated Label ID: Label_3070697212981345438\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Uber Receipts -> Designated Label ID: Label_7198370523552713631\\nCriteria: ... [criteria] ...\\nExample Agent Execution Flow:\\nInput: Email (Unimportant offer), message_id_variable = \\\"msg_abc123\\\"\\nAI Reasoning (Internal):\\n(Calls gmail_list_label if needed).\\nAnalyzes email content -> Matches 'Unimportant'.\\nLooks up 'Unimportant' -> Designated ID is Label_14.\\nPrepare tool call: label_id='Label_14', message_id='msg_abc123'.\\nAI Action: Calls gmail_add_label(label_id='Label_14', message_id='msg_abc123').\\n(Assume Tool Call Succeeds)\\nAI Final Output: \\\"Successfully called gmail_add_label to apply Label ID 'Label_14' to message 'msg_abc123'.\\\"\\nHuman: ID: 198198ab515315e7\\nFrom: Indeed <<EMAIL>>\\nSubject: Host @ Areas USA, Inc\\nBody: $20.10 an hour. Hi jalen, It looks like your background could be a match for this Host role. Please submit a quick application if you have any interest. ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌\\nAI: model, [\\n  {\\n    \\\"functionCall\\\": {\\n      \\\"name\\\": \\\"Add_Label\\\",\\n      \\\"args\\\": {\\n        \\\"Label_Names_or_IDs\\\": \\\"Label_21\\\",\\n        \\\"Message_ID\\\": \\\"198198ab515315e7\\\"\\n      }\\n    },\\n    \\\"thoughtSignature\\\": \\\"Ci0BVKhc7uKN4WlvgqIn4L8CSSO1xR4ytfj0l7qcVpZBgRPxZEpVtA/A8ww/Z/4KbAFUqFzusHaWzGMstTvHIL8KtPc1mv19zyD996awMSMb26WXHSW6di74d2c3/O+8qFjf52WdBEacE3I9ej7bFW0p/T4fYu+GmvlL3O8NmlXUfVfdhOHPdQQ16rkjpIyUdSRP06lrID1mDZn14Qr5AQFUqFzuT2y9po2ibsjS1KFOyH3q7TXxteBzDxm5vGEUxJDpOmNvXxkvTee6pT+ryArJFjzRcG+CwGhKY7/6YOCEUyxFj2ge7tv1Dzh+MDCvUm65mWHNaa2rgfv8TUY3RNU4/ReEO30CwohIQ5aFNCdXQfMhxHmjg18yuYw/J+XFcxRIfMo6K3J2d+CxGHSYYtPp+n9eG3Mf+xPUtpg4X5zXn7nQ3gKlsygydaqztOITQrinl9qDLpa+Fu6fvy0AlL6gjuw+fxdgsIMaj2A596wTl+jH/eezqRIwDNMa3nZ1GTjg2EwnMXbU3bHoL34MFpekLKpXxSAjCAq8AQFUqFzux1tnFEB4blx5QVoKCz0EfPYJF3dxVGtkIsPjw4Q8TciIPG25Dtj5tt8t+IviXiYBAHPbpvNvUfP35NsyFxPpLSOkVbRbXTD244ESDglaE6RaISIvTLGs7jmF1yD8GEdTQrF7Pd1aiTk0MDl0rbKzPyQj8K4rAfPTXJA7KKobdb9nQvSKqVMkEla8NaEKzj4oo7FCKyOFXsn4Xgd0WNhcdblHGQaWNo9h6dyRk8WN6WK87SC5zp+FCt0BAVSoXO4OcpC9v3LvQaDFUiIxI8sqHj40cxfp28Cmt1s5epB1jD2C8+eP1WhbXW4pXai9cxsMXaeiLWpVbpXORs0AZWLReL9OPl5CXjkyadFLl+cQz98T3YiwvQ3Y0am5y9fReyIVEfcKykhi/gQfos7uhoUM8obmJCPvJW4kxo8VHH7QfHfmifdCfl/52eSlspV3PqoJ5ocY19vlzJ1XFmhzf2nCL4e04ybLc1UHU8LEi6x/L90lAlU0gkO4yMyztIvM0cWg+T0zgwOEHSfehvnA7e3ErSnEVCP4HPwKhwIBVKhc7pozyz7rfth8Mg/bKv4R2vFWw/f11jD4B7LAY2JQV6+puKloWxnLwDHH/6QUBU443cq4gUMtrfRJncRnHS51G/uIiqzhNWKNXtDdM+v7iNNd9rpqKDT8JQRG7ce51eJU0rbCeMXnxC6CDhwFj5NNRlWHBGAEiBkImZbF/iVX73WqIYN6dHMzDPthBH6aAFvNetpn/RNF5nk0Ble6ZDTry6jsx/KntAcwctsZbm1uVwAuUhXFkZUGWaBdnC2qvEKVt2Jl3pztnFLloMHLDOIQ3WC/xEqxRkoWgTh6oNWS2KatKCy1P51H9OzfgIKQ0HVptFTVTWo/Ubet2jKd1N83C2XhtAqoAQFUqFzunoGt0NtZHpDe/L6aYFWOdNVLN6ynHItKlEi8aFCsoFU+IUmer9lUjNAUTqWcPKS2YDCvQdPO8lel3m96ePPcxvC/7cUgWuQKILDOEXXzXClI7XtcyF068seEKovFe3Cku1+KNViNpr9g8Toa1VEZnUIQs8gW5RX7jRaL3bw4ljTfspBfjD34o9iV5APvo6y3lFbK8JU4TtQM9teL3Ox77WmkkQp0AVSoXO54N5QpB+eWqyuJ41hTbomySEBRfOXxgNsecGFPpMAGzeyu+d93R799BzKipWAXGIhwClZ95TJwrOi9j/U8bXNUOhfCUiX4RaFAU9r/Uk+ubcEXAW/9IhzKIGck1ujbAZh+cqFj2hGtyZQ84Wo46aA=\\\"\\n  }\\n]\\nTool: [{\\\"type\\\":\\\"text\\\",\\\"text\\\":\\\"[{\\\\\\\"id\\\\\\\":\\\\\\\"198198ab515315e7\\\\\\\",\\\\\\\"threadId\\\\\\\":\\\\\\\"198198ab515315e7\\\\\\\",\\\\\\\"labelIds\\\\\\\":[\\\\\\\"UNREAD\\\\\\\",\\\\\\\"Label_21\\\\\\\",\\\\\\\"CATEGORY_UPDATES\\\\\\\",\\\\\\\"INBOX\\\\\\\"]}]\\\"}]\"],\"options\":{\"google_api_key\":{\"lc\":1,\"type\":\"secret\",\"id\":[\"GOOGLE_API_KEY\"]},\"base_url\":\"https://generativelanguage.googleapis.com\",\"model\":\"gemini-2.5-flash-preview-05-20\"},\"response\":{\"response\":{\"generations\":[[{\"text\":\"Successfully called gmail_add_label to apply Label ID 'Label_21' to message '198198ab515315e7'.\",\"generationInfo\":{\"index\":0,\"finishReason\":\"STOP\"}}]]},\"tokenUsageEstimate\":{\"completionTokens\":25,\"promptTokens\":2872,\"totalTokens\":2897}}}"}}
{"__type":"$$EventMessageConfirm","confirm":"183dab73-d74a-4e27-a9d9-3bc77c5270e8","ts":"2025-07-17T13:59:59.758-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"5a048407-f817-4e0d-988a-b7f5ae2e4b4f","ts":"2025-07-17T13:59:59.778-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8103","nodeType":"@n8n/n8n-nodes-langchain.agent","nodeName":"AI Agent","nodeId":"fa2efd94-444c-4efc-8f00-d76b47673a95"}}
{"__type":"$$EventMessageConfirm","confirm":"5a048407-f817-4e0d-988a-b7f5ae2e4b4f","ts":"2025-07-17T13:59:59.778-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageWorkflow","id":"0807e90c-1d22-47f8-bf0f-64903c9687f9","ts":"2025-07-17T13:59:59.778-04:00","eventName":"n8n.workflow.success","message":"n8n.workflow.success","payload":{"executionId":"8103","success":true,"isManual":false,"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler"}}
{"__type":"$$EventMessageConfirm","confirm":"0807e90c-1d22-47f8-bf0f-64903c9687f9","ts":"2025-07-17T13:59:59.778-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageWorkflow","id":"45a21316-0802-43ec-843a-708a8fea65b4","ts":"2025-07-17T14:00:28.004-04:00","eventName":"n8n.workflow.started","message":"n8n.workflow.started","payload":{"executionId":"8105","workflowId":"l5tiEZ9mwZAvOjTN","isManual":false,"workflowName":"Gmail trigger Agent"}}
{"__type":"$$EventMessageConfirm","confirm":"45a21316-0802-43ec-843a-708a8fea65b4","ts":"2025-07-17T14:00:28.004-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"65d36487-b17f-41bb-bb0b-8f81c925ede4","ts":"2025-07-17T14:00:28.005-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"l5tiEZ9mwZAvOjTN","workflowName":"Gmail trigger Agent","executionId":"8105","nodeType":"n8n-nodes-base.gmailTrigger","nodeName":"Gmail Trigger","nodeId":"a67441ca-d29f-49b3-bdc9-591d79ea421e"}}
{"__type":"$$EventMessageConfirm","confirm":"65d36487-b17f-41bb-bb0b-8f81c925ede4","ts":"2025-07-17T14:00:28.005-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"cf0708f5-4610-4df7-b66a-0877908aa686","ts":"2025-07-17T14:00:28.005-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"l5tiEZ9mwZAvOjTN","workflowName":"Gmail trigger Agent","executionId":"8105","nodeType":"n8n-nodes-base.gmailTrigger","nodeName":"Gmail Trigger","nodeId":"a67441ca-d29f-49b3-bdc9-591d79ea421e"}}
{"__type":"$$EventMessageConfirm","confirm":"cf0708f5-4610-4df7-b66a-0877908aa686","ts":"2025-07-17T14:00:28.005-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"795f9eea-59da-4bb8-939c-da55bd6303d9","ts":"2025-07-17T14:00:28.006-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"l5tiEZ9mwZAvOjTN","workflowName":"Gmail trigger Agent","executionId":"8105","nodeType":"@n8n/n8n-nodes-langchain.agent","nodeName":"AI Agent","nodeId":"65a6d9ea-00c8-40a2-a8a1-f2cf94129e1a"}}
{"__type":"$$EventMessageConfirm","confirm":"795f9eea-59da-4bb8-939c-da55bd6303d9","ts":"2025-07-17T14:00:28.006-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"34ad9ced-4006-4a0b-8c93-82b8af42ed04","ts":"2025-07-17T14:00:28.053-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"l5tiEZ9mwZAvOjTN","workflowName":"Gmail trigger Agent","executionId":"8105","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","nodeName":"Google Gemini Chat Model","nodeId":"7db1c3df-665d-4861-9248-640ebe1324a9"}}
{"__type":"$$EventMessageConfirm","confirm":"34ad9ced-4006-4a0b-8c93-82b8af42ed04","ts":"2025-07-17T14:00:28.053-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"6de5dcd4-5f14-411f-8a56-64f71ea05336","ts":"2025-07-17T14:00:30.945-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"l5tiEZ9mwZAvOjTN","workflowName":"Gmail trigger Agent","executionId":"8105","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","nodeName":"Google Gemini Chat Model","nodeId":"7db1c3df-665d-4861-9248-640ebe1324a9"}}
{"__type":"$$EventMessageConfirm","confirm":"6de5dcd4-5f14-411f-8a56-64f71ea05336","ts":"2025-07-17T14:00:30.945-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageAiNode","id":"2941b312-6d36-4d6b-b1d5-5b12ee0743c0","ts":"2025-07-17T14:00:30.945-04:00","eventName":"n8n.ai.llm.generated","message":"n8n.ai.llm.generated","payload":{"executionId":"8105","nodeName":"Google Gemini Chat Model","workflowName":"Gmail trigger Agent","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","workflowId":"l5tiEZ9mwZAvOjTN","msg":"{\"messages\":[\"System: You are an intelligent mailing assistant. Your role is to read structured email data and provide a clear, helpful summary for the user. Each email will be given in this format:\\n\\nmakefile\\nCopy\\nEdit\\nFrom: [sender name and email]\\nTO: [recipient email]\\nSubject: [email subject]\\nbody: [email message body]\\nlabels: [comma-separated label names]\\nYour job is to:\\n\\nSummarize the email clearly in one or two sentences.\\n\\nIdentify the email type: one of the following — personal, notification, promotion, transactional, or spam.\\n\\nSuggest a next action for the user, such as reply, mark as read, ignore, or archive.\\n\\nUse the labels to help infer context or priority.\\nHuman: From:  Indeed <<EMAIL>>\\nTO: <<EMAIL>>\\nSubject: Host @ Areas USA, Inc\\nbody: $20.10 an hour. Hi jalen, It looks like your background could be a match for this Host role. Please submit a quick application if you have any interest. ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌\\nlbales: INBOX, CATEGORY_UPDATES, UNREAD, Job Suggestions\"],\"options\":{\"google_api_key\":{\"lc\":1,\"type\":\"secret\",\"id\":[\"GOOGLE_API_KEY\"]},\"base_url\":\"https://generativelanguage.googleapis.com\",\"model\":\"gemini-2.5-flash-preview-05-20\"},\"response\":{\"response\":{\"generations\":[[{\"text\":\"Here's a summary of the email:\\n\\nThis email from Indeed suggests a Host position at Areas USA, Inc. for $20.10 an hour, encouraging you to submit an application if interested.\\n\\nEmail Type: Notification\\n\\nNext Action: Apply if interested, or archive.\",\"generationInfo\":{\"index\":0,\"finishReason\":\"STOP\"}}]]},\"tokenUsage\":{\"completionTokens\":14,\"promptTokens\":0,\"totalTokens\":14}}}"}}
{"__type":"$$EventMessageConfirm","confirm":"2941b312-6d36-4d6b-b1d5-5b12ee0743c0","ts":"2025-07-17T14:00:30.945-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"641b37f5-afa5-453d-8df1-2ae36031f1d8","ts":"2025-07-17T14:00:30.948-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"l5tiEZ9mwZAvOjTN","workflowName":"Gmail trigger Agent","executionId":"8105","nodeType":"@n8n/n8n-nodes-langchain.agent","nodeName":"AI Agent","nodeId":"65a6d9ea-00c8-40a2-a8a1-f2cf94129e1a"}}
{"__type":"$$EventMessageConfirm","confirm":"641b37f5-afa5-453d-8df1-2ae36031f1d8","ts":"2025-07-17T14:00:30.948-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageWorkflow","id":"0df8a52f-39aa-459c-9348-1a332c7717bf","ts":"2025-07-17T14:00:30.950-04:00","eventName":"n8n.workflow.success","message":"n8n.workflow.success","payload":{"executionId":"8105","success":true,"isManual":false,"workflowId":"l5tiEZ9mwZAvOjTN","workflowName":"Gmail trigger Agent"}}
{"__type":"$$EventMessageConfirm","confirm":"0df8a52f-39aa-459c-9348-1a332c7717bf","ts":"2025-07-17T14:00:30.950-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageWorkflow","id":"3eb19284-c824-4325-8de9-594b7a165ad0","ts":"2025-07-17T14:04:53.897-04:00","eventName":"n8n.workflow.started","message":"n8n.workflow.started","payload":{"executionId":"8106","workflowId":"7I3EfxpsDgrByI8X","isManual":false,"workflowName":"Email Labler"}}
{"__type":"$$EventMessageConfirm","confirm":"3eb19284-c824-4325-8de9-594b7a165ad0","ts":"2025-07-17T14:04:53.897-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"8316dd9a-da78-4b09-bb97-1befff4219c2","ts":"2025-07-17T14:04:53.897-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8106","nodeType":"n8n-nodes-base.gmailTrigger","nodeName":"Gmail Trigger","nodeId":"6738eab9-dd67-4bb1-b0d7-d376f6c65fce"}}
{"__type":"$$EventMessageConfirm","confirm":"8316dd9a-da78-4b09-bb97-1befff4219c2","ts":"2025-07-17T14:04:53.897-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"95d855eb-d99d-4d2a-bdc8-aa934df74d48","ts":"2025-07-17T14:04:53.897-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8106","nodeType":"n8n-nodes-base.gmailTrigger","nodeName":"Gmail Trigger","nodeId":"6738eab9-dd67-4bb1-b0d7-d376f6c65fce"}}
{"__type":"$$EventMessageConfirm","confirm":"95d855eb-d99d-4d2a-bdc8-aa934df74d48","ts":"2025-07-17T14:04:53.897-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"03ced3af-a239-479f-823d-908d163e485d","ts":"2025-07-17T14:04:53.897-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8106","nodeType":"@n8n/n8n-nodes-langchain.agent","nodeName":"AI Agent","nodeId":"fa2efd94-444c-4efc-8f00-d76b47673a95"}}
{"__type":"$$EventMessageConfirm","confirm":"03ced3af-a239-479f-823d-908d163e485d","ts":"2025-07-17T14:04:53.897-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"770f27e9-9e99-4970-a354-9d09f1739275","ts":"2025-07-17T14:04:53.960-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8106","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","nodeName":"Google Gemini Chat Model","nodeId":"2431cbe7-32f6-4b11-a623-da84241e6dd2"}}
{"__type":"$$EventMessageConfirm","confirm":"770f27e9-9e99-4970-a354-9d09f1739275","ts":"2025-07-17T14:04:53.960-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"f9b9dc56-06aa-4ec6-9bdc-78999c15e9ee","ts":"2025-07-17T14:04:58.519-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8106","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","nodeName":"Google Gemini Chat Model","nodeId":"2431cbe7-32f6-4b11-a623-da84241e6dd2"}}
{"__type":"$$EventMessageConfirm","confirm":"f9b9dc56-06aa-4ec6-9bdc-78999c15e9ee","ts":"2025-07-17T14:04:58.520-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageAiNode","id":"4c16439a-50bc-40bc-a168-508600bfe7bd","ts":"2025-07-17T14:04:58.520-04:00","eventName":"n8n.ai.llm.generated","message":"n8n.ai.llm.generated","payload":{"executionId":"8106","nodeName":"Google Gemini Chat Model","workflowName":"Email Labler","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","workflowId":"7I3EfxpsDgrByI8X","msg":"{\"messages\":[\"System: You are an AI Email Classification Agent operating within a system that provides access to specific Gmail tools (gmail_list_label, gmail_add_label) via a model context protocol server. Your task is to:\\nDetermine the correct existing Label ID for an incoming email based solely on the definitive list of labels provided below.\\nExecute the labeling by calling the gmail_add_label tool with the determined Label ID and the email's Message ID.\\nInputs:\\nIncoming Email (Sender, Subject, Body)\\nEmail Message ID (Let's assume this is available to you as message_id_variable)\\nProcedural Step: Although the definitive list of labels is provided below, you may need to invoke the gmail_list_label tool as the initial step if required by the system framework. However, for determining the correct label ID, you must strictly adhere to the names and IDs listed in the \\\"Definitive Label List\\\" section below.\\nDefinitive Label List (Source of Truth for Names and IDs):\\nCHAT / CHAT (System Label - Ignore)\\nSENT / SENT (System Label - Ignore)\\nINBOX / INBOX (System Label - Ignore)\\nIMPORTANT / IMPORTANT (System Label - Use ID IMPORTANT for the 'Important' conceptual category)\\nTRASH / TRASH (System Label - Ignore)\\nDRAFT / DRAFT (System Label - Ignore)\\nSPAM / SPAM (System Label - Ignore)\\n... (other system/category labels to ignore for classification) ...\\nInbox Zero / Label_12 (User Label - Ignore)\\nInbox Zero/Acted / Label_13 (User Label - Ignore)\\nUnimportant / Label_14 (User Label - Use for 'Unimportant' category)\\nVerification Codes / Label_15 (User Label - Use for 'Verification Codes' category)\\nApplication Status / Label_16 (User Label - Use for 'Application Status' category)\\nMeeting Confirmed / Label_17 (User Label - Use for 'Meeting Confirmed' category)\\nMessenger Notifications / Label_18 (User Label - Use for 'Messenger Notifications' category)\\nRocket Money Updates / Label_19 (User Label - Use for 'Rocket Money Updates' category)\\nApple Music Analytics / Label_20 (User Label - Use for 'Apple Music Analytics' category)\\nJob Suggestions / Label_21 (User Label - Use for 'Job Suggestions' category)\\nAi-Agent / Label_2878194751042528826 (User Label - Ignore)\\nYoutube Comments / Label_3070697212981345438 (User Label - Use for 'Youtube Comments' category)\\nAi-Agent/Checked / Label_6685525344120202453 (User Label - Ignore)\\nUber Receipts / Label_7198370523552713631 (User Label - Use for 'Uber Receipts' category)\\nAi-Agent/Checked/Unsure / Label_7805829971573720853 (User Label - Ignore)\\n(This list represents all labels currently recognized for classification based on the rules below).\\nClassification and Execution Process:\\n(Procedural Step - If required): Call gmail_list_label.\\nAnalyze Input: Examine the incoming email (Sender, Subject, Body) and note the message_id_variable.\\nMatch Criteria: Compare the email content against the Criteria for the Conceptual Label Names listed below. Apply rules and exclusions strictly.\\nSelect Category: Determine the single most appropriate Conceptual Label Name.\\nIdentify Designated ID: Find the selected Conceptual Label Name in the rules section below and identify its Designated Label ID based on the Definitive Label List above.\\nFallback Logic:\\nIf the email doesn't fit any specific category (1, 2, 5-11), use the 'Important' fallback (Designated ID: IMPORTANT).\\nIf it doesn't fit any specific category or 'Important', and doesn't hit 'Unimportant' exclusions, use the 'Unimportant' fallback (Designated ID: Label_14).\\nIf no category applies even after fallbacks, stop and indicate failure (NO_CATEGORY_MATCH).\\nPrepare Tool Call: You have now determined the target_label_id (the Designated Label ID from step 5 or 6).\\nExecute Labeling Action: Call the gmail_add_label tool. You MUST provide the following parameters:\\nlabel_id: The target_label_id you determined.\\nmessage_id: The message_id_variable provided in the input.\\nFinal Output: Report the outcome of the tool call. For example: \\\"Successfully called gmail_add_label to apply Label ID '[target_label_id]' to message '[message_id_variable]'.\\\" or \\\"Failed to apply label: [Reason from tool call]\\\". Do not just output the Label ID.\\nConceptual Label Names, Criteria, and Designated IDs:\\n(Match email to criteria. Use the corresponding Designated Label ID from the Definitive List above when calling gmail_add_label.)\\nConceptual Label Name: Application Status -> Designated Label ID: Label_16\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Job Suggestions -> Designated Label ID: Label_21\\nCriteria: ... [criteria & exclusions] ...\\nConceptual Label Name: Important -> Designated Label ID: IMPORTANT\\nCriteria: ... [criteria - fallback 1] ...\\nConceptual Label Name: Unimportant -> Designated Label ID: Label_14\\nCriteria: ... [criteria & exclusions - fallback 2] ...\\nConceptual Label Name: Verification Codes -> Designated Label ID: Label_15\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Meeting Confirmed -> Designated Label ID: Label_17\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Apple Music Analytics -> Designated Label ID: Label_20\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Rocket Money Updates -> Designated Label ID: Label_19\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Messenger Notifications -> Designated Label ID: Label_18\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Youtube Comments -> Designated Label ID: Label_3070697212981345438\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Uber Receipts -> Designated Label ID: Label_7198370523552713631\\nCriteria: ... [criteria] ...\\nExample Agent Execution Flow:\\nInput: Email (Unimportant offer), message_id_variable = \\\"msg_abc123\\\"\\nAI Reasoning (Internal):\\n(Calls gmail_list_label if needed).\\nAnalyzes email content -> Matches 'Unimportant'.\\nLooks up 'Unimportant' -> Designated ID is Label_14.\\nPrepare tool call: label_id='Label_14', message_id='msg_abc123'.\\nAI Action: Calls gmail_add_label(label_id='Label_14', message_id='msg_abc123').\\n(Assume Tool Call Succeeds)\\nAI Final Output: \\\"Successfully called gmail_add_label to apply Label ID 'Label_14' to message 'msg_abc123'.\\\"\\nHuman: ID: 198198f242da8748\\nFrom: Affinity Plus <<EMAIL>>\\nSubject: There’s Still Time to Double Your Affinity Plus Foundation Donation!\\nBody: Be a part of something bigger with the Affinity Plus Foundation. To view this email as a web page, go here. Affinity Plus Foundation Your donation can create brighter futures for local communities.\"],\"options\":{\"google_api_key\":{\"lc\":1,\"type\":\"secret\",\"id\":[\"GOOGLE_API_KEY\"]},\"base_url\":\"https://generativelanguage.googleapis.com\",\"model\":\"gemini-2.5-flash-preview-05-20\"},\"response\":{\"response\":{\"generations\":[[{\"text\":\"\",\"generationInfo\":{\"finishReason\":\"STOP\",\"index\":0}}]]},\"tokenUsage\":{\"completionTokens\":45,\"promptTokens\":1780,\"totalTokens\":1825}}}"}}
{"__type":"$$EventMessageConfirm","confirm":"4c16439a-50bc-40bc-a168-508600bfe7bd","ts":"2025-07-17T14:04:58.520-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"216ffcd7-4654-4c35-917c-9b52f650c6ca","ts":"2025-07-17T14:04:58.520-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8106","nodeType":"@n8n/n8n-nodes-langchain.mcpClientTool","nodeName":"Gmail Tools","nodeId":"1bf6d8f2-728a-43e7-b1c0-f35da20d0b76"}}
{"__type":"$$EventMessageConfirm","confirm":"216ffcd7-4654-4c35-917c-9b52f650c6ca","ts":"2025-07-17T14:04:58.520-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageAiNode","id":"fa589c4d-8cc4-4cda-86a3-36069f0a3493","ts":"2025-07-17T14:04:58.874-04:00","eventName":"n8n.ai.tool.called","message":"n8n.ai.tool.called","payload":{"executionId":"8106","nodeName":"Gmail Tools","workflowName":"Email Labler","nodeType":"@n8n/n8n-nodes-langchain.mcpClientTool","workflowId":"7I3EfxpsDgrByI8X","msg":"{\"query\":{\"Message_ID\":\"198198f242da8748\",\"Label_Names_or_IDs\":\"Label_14\"},\"tool\":{\"name\":\"Add_Label\",\"description\":\"Add label to message in Gmail\"},\"response\":[{\"type\":\"text\",\"text\":\"[{\\\"id\\\":\\\"198198f242da8748\\\",\\\"threadId\\\":\\\"198198f242da8748\\\",\\\"labelIds\\\":[\\\"CATEGORY_PROMOTIONS\\\",\\\"UNREAD\\\",\\\"Label_14\\\",\\\"INBOX\\\"]}]\"}]}"}}
{"__type":"$$EventMessageConfirm","confirm":"fa589c4d-8cc4-4cda-86a3-36069f0a3493","ts":"2025-07-17T14:04:58.874-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"31392968-e990-40ba-8974-4337dca71984","ts":"2025-07-17T14:04:58.874-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8106","nodeType":"@n8n/n8n-nodes-langchain.mcpClientTool","nodeName":"Gmail Tools","nodeId":"1bf6d8f2-728a-43e7-b1c0-f35da20d0b76"}}
{"__type":"$$EventMessageConfirm","confirm":"31392968-e990-40ba-8974-4337dca71984","ts":"2025-07-17T14:04:58.874-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"250689b4-90bf-41a7-b899-8d2376153d3d","ts":"2025-07-17T14:04:58.886-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8106","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","nodeName":"Google Gemini Chat Model","nodeId":"2431cbe7-32f6-4b11-a623-da84241e6dd2"}}
{"__type":"$$EventMessageConfirm","confirm":"250689b4-90bf-41a7-b899-8d2376153d3d","ts":"2025-07-17T14:04:58.886-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageWorkflow","id":"3777de5e-311f-40e1-9f90-760a19988e5f","ts":"2025-07-17T14:04:58.968-04:00","eventName":"n8n.workflow.started","message":"n8n.workflow.started","payload":{"executionId":"8107","workflowId":"7I3EfxpsDgrByI8X","isManual":false,"workflowName":"Email Labler"}}
{"__type":"$$EventMessageConfirm","confirm":"3777de5e-311f-40e1-9f90-760a19988e5f","ts":"2025-07-17T14:04:58.968-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"acc3f2dd-df5f-4f97-a51c-904a6dbf5450","ts":"2025-07-17T14:04:58.968-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8107","nodeType":"@n8n/n8n-nodes-langchain.mcpTrigger","nodeName":"MCP Server Trigger","nodeId":"738db1fc-738d-4d2b-9778-697d652bf9d3"}}
{"__type":"$$EventMessageConfirm","confirm":"acc3f2dd-df5f-4f97-a51c-904a6dbf5450","ts":"2025-07-17T14:04:58.968-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"907ca021-65ae-4c7f-8a6f-adb0fcb3f896","ts":"2025-07-17T14:04:58.968-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8107","nodeType":"@n8n/n8n-nodes-langchain.mcpTrigger","nodeName":"MCP Server Trigger","nodeId":"738db1fc-738d-4d2b-9778-697d652bf9d3"}}
{"__type":"$$EventMessageConfirm","confirm":"907ca021-65ae-4c7f-8a6f-adb0fcb3f896","ts":"2025-07-17T14:04:58.968-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageWorkflow","id":"8b42ddcc-2509-41d9-aa10-d4471f1508dc","ts":"2025-07-17T14:04:58.968-04:00","eventName":"n8n.workflow.success","message":"n8n.workflow.success","payload":{"executionId":"8107","success":true,"isManual":false,"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler"}}
{"__type":"$$EventMessageConfirm","confirm":"8b42ddcc-2509-41d9-aa10-d4471f1508dc","ts":"2025-07-17T14:04:58.968-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"5f1344a7-53c5-4c47-a95e-4a030b865659","ts":"2025-07-17T14:04:59.536-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8106","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","nodeName":"Google Gemini Chat Model","nodeId":"2431cbe7-32f6-4b11-a623-da84241e6dd2"}}
{"__type":"$$EventMessageConfirm","confirm":"5f1344a7-53c5-4c47-a95e-4a030b865659","ts":"2025-07-17T14:04:59.536-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageAiNode","id":"161c1068-3423-46c9-aab4-24264ffbe7d8","ts":"2025-07-17T14:04:59.536-04:00","eventName":"n8n.ai.llm.generated","message":"n8n.ai.llm.generated","payload":{"executionId":"8106","nodeName":"Google Gemini Chat Model","workflowName":"Email Labler","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","workflowId":"7I3EfxpsDgrByI8X","msg":"{\"messages\":[\"System: You are an AI Email Classification Agent operating within a system that provides access to specific Gmail tools (gmail_list_label, gmail_add_label) via a model context protocol server. Your task is to:\\nDetermine the correct existing Label ID for an incoming email based solely on the definitive list of labels provided below.\\nExecute the labeling by calling the gmail_add_label tool with the determined Label ID and the email's Message ID.\\nInputs:\\nIncoming Email (Sender, Subject, Body)\\nEmail Message ID (Let's assume this is available to you as message_id_variable)\\nProcedural Step: Although the definitive list of labels is provided below, you may need to invoke the gmail_list_label tool as the initial step if required by the system framework. However, for determining the correct label ID, you must strictly adhere to the names and IDs listed in the \\\"Definitive Label List\\\" section below.\\nDefinitive Label List (Source of Truth for Names and IDs):\\nCHAT / CHAT (System Label - Ignore)\\nSENT / SENT (System Label - Ignore)\\nINBOX / INBOX (System Label - Ignore)\\nIMPORTANT / IMPORTANT (System Label - Use ID IMPORTANT for the 'Important' conceptual category)\\nTRASH / TRASH (System Label - Ignore)\\nDRAFT / DRAFT (System Label - Ignore)\\nSPAM / SPAM (System Label - Ignore)\\n... (other system/category labels to ignore for classification) ...\\nInbox Zero / Label_12 (User Label - Ignore)\\nInbox Zero/Acted / Label_13 (User Label - Ignore)\\nUnimportant / Label_14 (User Label - Use for 'Unimportant' category)\\nVerification Codes / Label_15 (User Label - Use for 'Verification Codes' category)\\nApplication Status / Label_16 (User Label - Use for 'Application Status' category)\\nMeeting Confirmed / Label_17 (User Label - Use for 'Meeting Confirmed' category)\\nMessenger Notifications / Label_18 (User Label - Use for 'Messenger Notifications' category)\\nRocket Money Updates / Label_19 (User Label - Use for 'Rocket Money Updates' category)\\nApple Music Analytics / Label_20 (User Label - Use for 'Apple Music Analytics' category)\\nJob Suggestions / Label_21 (User Label - Use for 'Job Suggestions' category)\\nAi-Agent / Label_2878194751042528826 (User Label - Ignore)\\nYoutube Comments / Label_3070697212981345438 (User Label - Use for 'Youtube Comments' category)\\nAi-Agent/Checked / Label_6685525344120202453 (User Label - Ignore)\\nUber Receipts / Label_7198370523552713631 (User Label - Use for 'Uber Receipts' category)\\nAi-Agent/Checked/Unsure / Label_7805829971573720853 (User Label - Ignore)\\n(This list represents all labels currently recognized for classification based on the rules below).\\nClassification and Execution Process:\\n(Procedural Step - If required): Call gmail_list_label.\\nAnalyze Input: Examine the incoming email (Sender, Subject, Body) and note the message_id_variable.\\nMatch Criteria: Compare the email content against the Criteria for the Conceptual Label Names listed below. Apply rules and exclusions strictly.\\nSelect Category: Determine the single most appropriate Conceptual Label Name.\\nIdentify Designated ID: Find the selected Conceptual Label Name in the rules section below and identify its Designated Label ID based on the Definitive Label List above.\\nFallback Logic:\\nIf the email doesn't fit any specific category (1, 2, 5-11), use the 'Important' fallback (Designated ID: IMPORTANT).\\nIf it doesn't fit any specific category or 'Important', and doesn't hit 'Unimportant' exclusions, use the 'Unimportant' fallback (Designated ID: Label_14).\\nIf no category applies even after fallbacks, stop and indicate failure (NO_CATEGORY_MATCH).\\nPrepare Tool Call: You have now determined the target_label_id (the Designated Label ID from step 5 or 6).\\nExecute Labeling Action: Call the gmail_add_label tool. You MUST provide the following parameters:\\nlabel_id: The target_label_id you determined.\\nmessage_id: The message_id_variable provided in the input.\\nFinal Output: Report the outcome of the tool call. For example: \\\"Successfully called gmail_add_label to apply Label ID '[target_label_id]' to message '[message_id_variable]'.\\\" or \\\"Failed to apply label: [Reason from tool call]\\\". Do not just output the Label ID.\\nConceptual Label Names, Criteria, and Designated IDs:\\n(Match email to criteria. Use the corresponding Designated Label ID from the Definitive List above when calling gmail_add_label.)\\nConceptual Label Name: Application Status -> Designated Label ID: Label_16\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Job Suggestions -> Designated Label ID: Label_21\\nCriteria: ... [criteria & exclusions] ...\\nConceptual Label Name: Important -> Designated Label ID: IMPORTANT\\nCriteria: ... [criteria - fallback 1] ...\\nConceptual Label Name: Unimportant -> Designated Label ID: Label_14\\nCriteria: ... [criteria & exclusions - fallback 2] ...\\nConceptual Label Name: Verification Codes -> Designated Label ID: Label_15\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Meeting Confirmed -> Designated Label ID: Label_17\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Apple Music Analytics -> Designated Label ID: Label_20\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Rocket Money Updates -> Designated Label ID: Label_19\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Messenger Notifications -> Designated Label ID: Label_18\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Youtube Comments -> Designated Label ID: Label_3070697212981345438\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Uber Receipts -> Designated Label ID: Label_7198370523552713631\\nCriteria: ... [criteria] ...\\nExample Agent Execution Flow:\\nInput: Email (Unimportant offer), message_id_variable = \\\"msg_abc123\\\"\\nAI Reasoning (Internal):\\n(Calls gmail_list_label if needed).\\nAnalyzes email content -> Matches 'Unimportant'.\\nLooks up 'Unimportant' -> Designated ID is Label_14.\\nPrepare tool call: label_id='Label_14', message_id='msg_abc123'.\\nAI Action: Calls gmail_add_label(label_id='Label_14', message_id='msg_abc123').\\n(Assume Tool Call Succeeds)\\nAI Final Output: \\\"Successfully called gmail_add_label to apply Label ID 'Label_14' to message 'msg_abc123'.\\\"\\nHuman: ID: 198198f242da8748\\nFrom: Affinity Plus <<EMAIL>>\\nSubject: There’s Still Time to Double Your Affinity Plus Foundation Donation!\\nBody: Be a part of something bigger with the Affinity Plus Foundation. To view this email as a web page, go here. Affinity Plus Foundation Your donation can create brighter futures for local communities.\\nAI: model, [\\n  {\\n    \\\"functionCall\\\": {\\n      \\\"name\\\": \\\"Add_Label\\\",\\n      \\\"args\\\": {\\n        \\\"Label_Names_or_IDs\\\": \\\"Label_14\\\",\\n        \\\"Message_ID\\\": \\\"198198f242da8748\\\"\\n      }\\n    },\\n    \\\"thoughtSignature\\\": \\\"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\\\"\\n  }\\n]\\nTool: [{\\\"type\\\":\\\"text\\\",\\\"text\\\":\\\"[{\\\\\\\"id\\\\\\\":\\\\\\\"198198f242da8748\\\\\\\",\\\\\\\"threadId\\\\\\\":\\\\\\\"198198f242da8748\\\\\\\",\\\\\\\"labelIds\\\\\\\":[\\\\\\\"CATEGORY_PROMOTIONS\\\\\\\",\\\\\\\"UNREAD\\\\\\\",\\\\\\\"Label_14\\\\\\\",\\\\\\\"INBOX\\\\\\\"]}]\\\"}]\"],\"options\":{\"google_api_key\":{\"lc\":1,\"type\":\"secret\",\"id\":[\"GOOGLE_API_KEY\"]},\"base_url\":\"https://generativelanguage.googleapis.com\",\"model\":\"gemini-2.5-flash-preview-05-20\"},\"response\":{\"response\":{\"generations\":[[{\"text\":\"Successfully called gmail_add_label to apply Label ID 'Label_14' to message '198198f242da8748'.\",\"generationInfo\":{\"index\":0,\"finishReason\":\"STOP\"}}]]},\"tokenUsage\":{\"completionTokens\":18,\"promptTokens\":0,\"totalTokens\":18}}}"}}
{"__type":"$$EventMessageConfirm","confirm":"161c1068-3423-46c9-aab4-24264ffbe7d8","ts":"2025-07-17T14:04:59.537-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"d7b23e61-9375-408b-b70c-79c1950dbb38","ts":"2025-07-17T14:04:59.539-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8106","nodeType":"@n8n/n8n-nodes-langchain.agent","nodeName":"AI Agent","nodeId":"fa2efd94-444c-4efc-8f00-d76b47673a95"}}
{"__type":"$$EventMessageConfirm","confirm":"d7b23e61-9375-408b-b70c-79c1950dbb38","ts":"2025-07-17T14:04:59.539-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageWorkflow","id":"af21bf3d-3cf7-4ac6-8e4b-5f52ffb55ba0","ts":"2025-07-17T14:04:59.539-04:00","eventName":"n8n.workflow.success","message":"n8n.workflow.success","payload":{"executionId":"8106","success":true,"isManual":false,"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler"}}
{"__type":"$$EventMessageConfirm","confirm":"af21bf3d-3cf7-4ac6-8e4b-5f52ffb55ba0","ts":"2025-07-17T14:04:59.539-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageWorkflow","id":"e1095b5b-0fb7-4ac9-ac72-5a1d1970ee0e","ts":"2025-07-17T14:05:28.059-04:00","eventName":"n8n.workflow.started","message":"n8n.workflow.started","payload":{"executionId":"8108","workflowId":"l5tiEZ9mwZAvOjTN","isManual":false,"workflowName":"Gmail trigger Agent"}}
{"__type":"$$EventMessageConfirm","confirm":"e1095b5b-0fb7-4ac9-ac72-5a1d1970ee0e","ts":"2025-07-17T14:05:28.059-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"9b8be012-a12a-4956-a9cc-8ce8699ce74e","ts":"2025-07-17T14:05:28.059-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"l5tiEZ9mwZAvOjTN","workflowName":"Gmail trigger Agent","executionId":"8108","nodeType":"n8n-nodes-base.gmailTrigger","nodeName":"Gmail Trigger","nodeId":"a67441ca-d29f-49b3-bdc9-591d79ea421e"}}
{"__type":"$$EventMessageConfirm","confirm":"9b8be012-a12a-4956-a9cc-8ce8699ce74e","ts":"2025-07-17T14:05:28.059-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"b61bad3c-a98d-4461-8398-5b3fb880ce0b","ts":"2025-07-17T14:05:28.059-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"l5tiEZ9mwZAvOjTN","workflowName":"Gmail trigger Agent","executionId":"8108","nodeType":"n8n-nodes-base.gmailTrigger","nodeName":"Gmail Trigger","nodeId":"a67441ca-d29f-49b3-bdc9-591d79ea421e"}}
{"__type":"$$EventMessageConfirm","confirm":"b61bad3c-a98d-4461-8398-5b3fb880ce0b","ts":"2025-07-17T14:05:28.059-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"f01a2045-c281-41c2-b7c7-ccf5f300a3dc","ts":"2025-07-17T14:05:28.059-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"l5tiEZ9mwZAvOjTN","workflowName":"Gmail trigger Agent","executionId":"8108","nodeType":"@n8n/n8n-nodes-langchain.agent","nodeName":"AI Agent","nodeId":"65a6d9ea-00c8-40a2-a8a1-f2cf94129e1a"}}
{"__type":"$$EventMessageConfirm","confirm":"f01a2045-c281-41c2-b7c7-ccf5f300a3dc","ts":"2025-07-17T14:05:28.059-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"e091faf8-f9e0-48d6-81ba-d13dc463ac5c","ts":"2025-07-17T14:05:28.065-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"l5tiEZ9mwZAvOjTN","workflowName":"Gmail trigger Agent","executionId":"8108","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","nodeName":"Google Gemini Chat Model","nodeId":"7db1c3df-665d-4861-9248-640ebe1324a9"}}
{"__type":"$$EventMessageConfirm","confirm":"e091faf8-f9e0-48d6-81ba-d13dc463ac5c","ts":"2025-07-17T14:05:28.065-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"482098e1-a599-4d80-987d-9e1e2c79ab76","ts":"2025-07-17T14:05:29.714-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"l5tiEZ9mwZAvOjTN","workflowName":"Gmail trigger Agent","executionId":"8108","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","nodeName":"Google Gemini Chat Model","nodeId":"7db1c3df-665d-4861-9248-640ebe1324a9"}}
{"__type":"$$EventMessageConfirm","confirm":"482098e1-a599-4d80-987d-9e1e2c79ab76","ts":"2025-07-17T14:05:29.714-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageAiNode","id":"c6b9cbc3-3810-4bf3-82f3-20dd28e5b29a","ts":"2025-07-17T14:05:29.714-04:00","eventName":"n8n.ai.llm.generated","message":"n8n.ai.llm.generated","payload":{"executionId":"8108","nodeName":"Google Gemini Chat Model","workflowName":"Gmail trigger Agent","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","workflowId":"l5tiEZ9mwZAvOjTN","msg":"{\"messages\":[\"System: You are an intelligent mailing assistant. Your role is to read structured email data and provide a clear, helpful summary for the user. Each email will be given in this format:\\n\\nmakefile\\nCopy\\nEdit\\nFrom: [sender name and email]\\nTO: [recipient email]\\nSubject: [email subject]\\nbody: [email message body]\\nlabels: [comma-separated label names]\\nYour job is to:\\n\\nSummarize the email clearly in one or two sentences.\\n\\nIdentify the email type: one of the following — personal, notification, promotion, transactional, or spam.\\n\\nSuggest a next action for the user, such as reply, mark as read, ignore, or archive.\\n\\nUse the labels to help infer context or priority.\\nHuman: From:  Affinity Plus <<EMAIL>>\\nTO: <<EMAIL>>\\nSubject: There’s Still Time to Double Your Affinity Plus Foundation Donation!\\nbody: Be a part of something bigger with the Affinity Plus Foundation. To view this email as a web page, go here. Affinity Plus Foundation Your donation can create brighter futures for local communities.\\nlbales: INBOX, CATEGORY_PROMOTIONS, UNREAD, Unimportant\"],\"options\":{\"google_api_key\":{\"lc\":1,\"type\":\"secret\",\"id\":[\"GOOGLE_API_KEY\"]},\"base_url\":\"https://generativelanguage.googleapis.com\",\"model\":\"gemini-2.5-flash-preview-05-20\"},\"response\":{\"response\":{\"generations\":[[{\"text\":\"This is a promotional email from Affinity Plus Foundation encouraging you to donate and stating there's still time to double your donation. This email is a promotion. You may want to archive or ignore it if you are not interested.\",\"generationInfo\":{\"index\":0,\"finishReason\":\"STOP\"}}]]},\"tokenUsage\":{\"completionTokens\":24,\"promptTokens\":0,\"totalTokens\":24}}}"}}
{"__type":"$$EventMessageConfirm","confirm":"c6b9cbc3-3810-4bf3-82f3-20dd28e5b29a","ts":"2025-07-17T14:05:29.714-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"84280714-843d-4ed8-8cbf-54618b621e1c","ts":"2025-07-17T14:05:29.715-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"l5tiEZ9mwZAvOjTN","workflowName":"Gmail trigger Agent","executionId":"8108","nodeType":"@n8n/n8n-nodes-langchain.agent","nodeName":"AI Agent","nodeId":"65a6d9ea-00c8-40a2-a8a1-f2cf94129e1a"}}
{"__type":"$$EventMessageConfirm","confirm":"84280714-843d-4ed8-8cbf-54618b621e1c","ts":"2025-07-17T14:05:29.715-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageWorkflow","id":"5fba3fa9-8a39-477d-9f05-e538da69c727","ts":"2025-07-17T14:05:29.716-04:00","eventName":"n8n.workflow.success","message":"n8n.workflow.success","payload":{"executionId":"8108","success":true,"isManual":false,"workflowId":"l5tiEZ9mwZAvOjTN","workflowName":"Gmail trigger Agent"}}
{"__type":"$$EventMessageConfirm","confirm":"5fba3fa9-8a39-477d-9f05-e538da69c727","ts":"2025-07-17T14:05:29.716-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageWorkflow","id":"acd1469f-bf69-45f9-87cd-ded5e7c7155b","ts":"2025-07-17T14:06:28.418-04:00","eventName":"n8n.workflow.started","message":"n8n.workflow.started","payload":{"executionId":"8109","workflowId":"l5tiEZ9mwZAvOjTN","isManual":false,"workflowName":"Gmail trigger Agent"}}
{"__type":"$$EventMessageConfirm","confirm":"acd1469f-bf69-45f9-87cd-ded5e7c7155b","ts":"2025-07-17T14:06:28.418-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"dc99a1a5-d019-4054-bb99-edf23f540f85","ts":"2025-07-17T14:06:28.418-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"l5tiEZ9mwZAvOjTN","workflowName":"Gmail trigger Agent","executionId":"8109","nodeType":"n8n-nodes-base.gmailTrigger","nodeName":"Gmail Trigger","nodeId":"a67441ca-d29f-49b3-bdc9-591d79ea421e"}}
{"__type":"$$EventMessageConfirm","confirm":"dc99a1a5-d019-4054-bb99-edf23f540f85","ts":"2025-07-17T14:06:28.418-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"2506f7c2-0645-48d2-b6ce-c82997416b1c","ts":"2025-07-17T14:06:28.418-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"l5tiEZ9mwZAvOjTN","workflowName":"Gmail trigger Agent","executionId":"8109","nodeType":"n8n-nodes-base.gmailTrigger","nodeName":"Gmail Trigger","nodeId":"a67441ca-d29f-49b3-bdc9-591d79ea421e"}}
{"__type":"$$EventMessageConfirm","confirm":"2506f7c2-0645-48d2-b6ce-c82997416b1c","ts":"2025-07-17T14:06:28.418-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"04fab15d-f5f1-4286-982d-05a6b1fc657b","ts":"2025-07-17T14:06:28.418-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"l5tiEZ9mwZAvOjTN","workflowName":"Gmail trigger Agent","executionId":"8109","nodeType":"@n8n/n8n-nodes-langchain.agent","nodeName":"AI Agent","nodeId":"65a6d9ea-00c8-40a2-a8a1-f2cf94129e1a"}}
{"__type":"$$EventMessageConfirm","confirm":"04fab15d-f5f1-4286-982d-05a6b1fc657b","ts":"2025-07-17T14:06:28.418-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"3e7472af-e6f9-4bff-b3f9-dc9374eea1ca","ts":"2025-07-17T14:06:28.425-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"l5tiEZ9mwZAvOjTN","workflowName":"Gmail trigger Agent","executionId":"8109","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","nodeName":"Google Gemini Chat Model","nodeId":"7db1c3df-665d-4861-9248-640ebe1324a9"}}
{"__type":"$$EventMessageConfirm","confirm":"3e7472af-e6f9-4bff-b3f9-dc9374eea1ca","ts":"2025-07-17T14:06:28.425-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"fa3edea0-e292-4346-aa0d-534377948dc3","ts":"2025-07-17T14:06:30.136-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"l5tiEZ9mwZAvOjTN","workflowName":"Gmail trigger Agent","executionId":"8109","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","nodeName":"Google Gemini Chat Model","nodeId":"7db1c3df-665d-4861-9248-640ebe1324a9"}}
{"__type":"$$EventMessageConfirm","confirm":"fa3edea0-e292-4346-aa0d-534377948dc3","ts":"2025-07-17T14:06:30.136-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageAiNode","id":"f1aad321-9baf-4782-b6b7-536a9c46d1b6","ts":"2025-07-17T14:06:30.136-04:00","eventName":"n8n.ai.llm.generated","message":"n8n.ai.llm.generated","payload":{"executionId":"8109","nodeName":"Google Gemini Chat Model","workflowName":"Gmail trigger Agent","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","workflowId":"l5tiEZ9mwZAvOjTN","msg":"{\"messages\":[\"System: You are an intelligent mailing assistant. Your role is to read structured email data and provide a clear, helpful summary for the user. Each email will be given in this format:\\n\\nmakefile\\nCopy\\nEdit\\nFrom: [sender name and email]\\nTO: [recipient email]\\nSubject: [email subject]\\nbody: [email message body]\\nlabels: [comma-separated label names]\\nYour job is to:\\n\\nSummarize the email clearly in one or two sentences.\\n\\nIdentify the email type: one of the following — personal, notification, promotion, transactional, or spam.\\n\\nSuggest a next action for the user, such as reply, mark as read, ignore, or archive.\\n\\nUse the labels to help infer context or priority.\\nHuman: From:  Cub <<EMAIL>>\\nTO: <<EMAIL>>\\nSubject: Pizza Night Made Easy with Culinary Circle🍕\\nbody: Shop your neighborhood Cub for your faves! To view this email as a web page, go here. culinary circle pizza Cub Instagram Cub Twitter Cub Facebook Cub Pinterest Copyright © 2025 Cub, All rights\\nlbales: INBOX, CATEGORY_PROMOTIONS, UNREAD\"],\"options\":{\"google_api_key\":{\"lc\":1,\"type\":\"secret\",\"id\":[\"GOOGLE_API_KEY\"]},\"base_url\":\"https://generativelanguage.googleapis.com\",\"model\":\"gemini-2.5-flash-preview-05-20\"},\"response\":{\"response\":{\"generations\":[[{\"text\":\"This is a promotional email from Cub advertising Culinary Circle pizza.\\n\\n**Email Type:** Promotion\\n\\n**Next Action:** Archive\",\"generationInfo\":{\"finishReason\":\"STOP\",\"index\":0}}]]},\"tokenUsage\":{\"completionTokens\":24,\"promptTokens\":296,\"totalTokens\":320}}}"}}
{"__type":"$$EventMessageConfirm","confirm":"f1aad321-9baf-4782-b6b7-536a9c46d1b6","ts":"2025-07-17T14:06:30.136-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"ee73fbb4-4a3b-471d-817d-4a297d9dbc16","ts":"2025-07-17T14:06:30.137-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"l5tiEZ9mwZAvOjTN","workflowName":"Gmail trigger Agent","executionId":"8109","nodeType":"@n8n/n8n-nodes-langchain.agent","nodeName":"AI Agent","nodeId":"65a6d9ea-00c8-40a2-a8a1-f2cf94129e1a"}}
{"__type":"$$EventMessageConfirm","confirm":"ee73fbb4-4a3b-471d-817d-4a297d9dbc16","ts":"2025-07-17T14:06:30.137-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageWorkflow","id":"05654cc9-3ab3-4db7-9cba-4df8e0c11883","ts":"2025-07-17T14:06:30.137-04:00","eventName":"n8n.workflow.success","message":"n8n.workflow.success","payload":{"executionId":"8109","success":true,"isManual":false,"workflowId":"l5tiEZ9mwZAvOjTN","workflowName":"Gmail trigger Agent"}}
{"__type":"$$EventMessageConfirm","confirm":"05654cc9-3ab3-4db7-9cba-4df8e0c11883","ts":"2025-07-17T14:06:30.137-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageWorkflow","id":"2b6262c0-fdab-4bcb-8248-2aa7e246e98b","ts":"2025-07-17T14:06:54.061-04:00","eventName":"n8n.workflow.started","message":"n8n.workflow.started","payload":{"executionId":"8110","workflowId":"7I3EfxpsDgrByI8X","isManual":false,"workflowName":"Email Labler"}}
{"__type":"$$EventMessageConfirm","confirm":"2b6262c0-fdab-4bcb-8248-2aa7e246e98b","ts":"2025-07-17T14:06:54.061-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"d2722f8e-baa3-4577-9628-956f6a83c49d","ts":"2025-07-17T14:06:54.061-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8110","nodeType":"n8n-nodes-base.gmailTrigger","nodeName":"Gmail Trigger","nodeId":"6738eab9-dd67-4bb1-b0d7-d376f6c65fce"}}
{"__type":"$$EventMessageConfirm","confirm":"d2722f8e-baa3-4577-9628-956f6a83c49d","ts":"2025-07-17T14:06:54.061-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"6fc45718-6d1d-40ce-a700-f3928a884c92","ts":"2025-07-17T14:06:54.061-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8110","nodeType":"n8n-nodes-base.gmailTrigger","nodeName":"Gmail Trigger","nodeId":"6738eab9-dd67-4bb1-b0d7-d376f6c65fce"}}
{"__type":"$$EventMessageConfirm","confirm":"6fc45718-6d1d-40ce-a700-f3928a884c92","ts":"2025-07-17T14:06:54.061-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"8d324f39-85e7-4eb1-adc1-1a8ed89b1aa3","ts":"2025-07-17T14:06:54.062-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8110","nodeType":"@n8n/n8n-nodes-langchain.agent","nodeName":"AI Agent","nodeId":"fa2efd94-444c-4efc-8f00-d76b47673a95"}}
{"__type":"$$EventMessageConfirm","confirm":"8d324f39-85e7-4eb1-adc1-1a8ed89b1aa3","ts":"2025-07-17T14:06:54.062-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"0598ec49-d515-4b71-989e-7dbb91e1a2a5","ts":"2025-07-17T14:06:54.120-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8110","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","nodeName":"Google Gemini Chat Model","nodeId":"2431cbe7-32f6-4b11-a623-da84241e6dd2"}}
{"__type":"$$EventMessageConfirm","confirm":"0598ec49-d515-4b71-989e-7dbb91e1a2a5","ts":"2025-07-17T14:06:54.120-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"323173b6-a914-4e93-af38-36d4708acc5a","ts":"2025-07-17T14:06:57.553-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8110","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","nodeName":"Google Gemini Chat Model","nodeId":"2431cbe7-32f6-4b11-a623-da84241e6dd2"}}
{"__type":"$$EventMessageConfirm","confirm":"323173b6-a914-4e93-af38-36d4708acc5a","ts":"2025-07-17T14:06:57.553-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageAiNode","id":"01b90337-5be1-4f53-bd66-58e279d3304f","ts":"2025-07-17T14:06:57.553-04:00","eventName":"n8n.ai.llm.generated","message":"n8n.ai.llm.generated","payload":{"executionId":"8110","nodeName":"Google Gemini Chat Model","workflowName":"Email Labler","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","workflowId":"7I3EfxpsDgrByI8X","msg":"{\"messages\":[\"System: You are an AI Email Classification Agent operating within a system that provides access to specific Gmail tools (gmail_list_label, gmail_add_label) via a model context protocol server. Your task is to:\\nDetermine the correct existing Label ID for an incoming email based solely on the definitive list of labels provided below.\\nExecute the labeling by calling the gmail_add_label tool with the determined Label ID and the email's Message ID.\\nInputs:\\nIncoming Email (Sender, Subject, Body)\\nEmail Message ID (Let's assume this is available to you as message_id_variable)\\nProcedural Step: Although the definitive list of labels is provided below, you may need to invoke the gmail_list_label tool as the initial step if required by the system framework. However, for determining the correct label ID, you must strictly adhere to the names and IDs listed in the \\\"Definitive Label List\\\" section below.\\nDefinitive Label List (Source of Truth for Names and IDs):\\nCHAT / CHAT (System Label - Ignore)\\nSENT / SENT (System Label - Ignore)\\nINBOX / INBOX (System Label - Ignore)\\nIMPORTANT / IMPORTANT (System Label - Use ID IMPORTANT for the 'Important' conceptual category)\\nTRASH / TRASH (System Label - Ignore)\\nDRAFT / DRAFT (System Label - Ignore)\\nSPAM / SPAM (System Label - Ignore)\\n... (other system/category labels to ignore for classification) ...\\nInbox Zero / Label_12 (User Label - Ignore)\\nInbox Zero/Acted / Label_13 (User Label - Ignore)\\nUnimportant / Label_14 (User Label - Use for 'Unimportant' category)\\nVerification Codes / Label_15 (User Label - Use for 'Verification Codes' category)\\nApplication Status / Label_16 (User Label - Use for 'Application Status' category)\\nMeeting Confirmed / Label_17 (User Label - Use for 'Meeting Confirmed' category)\\nMessenger Notifications / Label_18 (User Label - Use for 'Messenger Notifications' category)\\nRocket Money Updates / Label_19 (User Label - Use for 'Rocket Money Updates' category)\\nApple Music Analytics / Label_20 (User Label - Use for 'Apple Music Analytics' category)\\nJob Suggestions / Label_21 (User Label - Use for 'Job Suggestions' category)\\nAi-Agent / Label_2878194751042528826 (User Label - Ignore)\\nYoutube Comments / Label_3070697212981345438 (User Label - Use for 'Youtube Comments' category)\\nAi-Agent/Checked / Label_6685525344120202453 (User Label - Ignore)\\nUber Receipts / Label_7198370523552713631 (User Label - Use for 'Uber Receipts' category)\\nAi-Agent/Checked/Unsure / Label_7805829971573720853 (User Label - Ignore)\\n(This list represents all labels currently recognized for classification based on the rules below).\\nClassification and Execution Process:\\n(Procedural Step - If required): Call gmail_list_label.\\nAnalyze Input: Examine the incoming email (Sender, Subject, Body) and note the message_id_variable.\\nMatch Criteria: Compare the email content against the Criteria for the Conceptual Label Names listed below. Apply rules and exclusions strictly.\\nSelect Category: Determine the single most appropriate Conceptual Label Name.\\nIdentify Designated ID: Find the selected Conceptual Label Name in the rules section below and identify its Designated Label ID based on the Definitive Label List above.\\nFallback Logic:\\nIf the email doesn't fit any specific category (1, 2, 5-11), use the 'Important' fallback (Designated ID: IMPORTANT).\\nIf it doesn't fit any specific category or 'Important', and doesn't hit 'Unimportant' exclusions, use the 'Unimportant' fallback (Designated ID: Label_14).\\nIf no category applies even after fallbacks, stop and indicate failure (NO_CATEGORY_MATCH).\\nPrepare Tool Call: You have now determined the target_label_id (the Designated Label ID from step 5 or 6).\\nExecute Labeling Action: Call the gmail_add_label tool. You MUST provide the following parameters:\\nlabel_id: The target_label_id you determined.\\nmessage_id: The message_id_variable provided in the input.\\nFinal Output: Report the outcome of the tool call. For example: \\\"Successfully called gmail_add_label to apply Label ID '[target_label_id]' to message '[message_id_variable]'.\\\" or \\\"Failed to apply label: [Reason from tool call]\\\". Do not just output the Label ID.\\nConceptual Label Names, Criteria, and Designated IDs:\\n(Match email to criteria. Use the corresponding Designated Label ID from the Definitive List above when calling gmail_add_label.)\\nConceptual Label Name: Application Status -> Designated Label ID: Label_16\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Job Suggestions -> Designated Label ID: Label_21\\nCriteria: ... [criteria & exclusions] ...\\nConceptual Label Name: Important -> Designated Label ID: IMPORTANT\\nCriteria: ... [criteria - fallback 1] ...\\nConceptual Label Name: Unimportant -> Designated Label ID: Label_14\\nCriteria: ... [criteria & exclusions - fallback 2] ...\\nConceptual Label Name: Verification Codes -> Designated Label ID: Label_15\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Meeting Confirmed -> Designated Label ID: Label_17\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Apple Music Analytics -> Designated Label ID: Label_20\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Rocket Money Updates -> Designated Label ID: Label_19\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Messenger Notifications -> Designated Label ID: Label_18\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Youtube Comments -> Designated Label ID: Label_3070697212981345438\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Uber Receipts -> Designated Label ID: Label_7198370523552713631\\nCriteria: ... [criteria] ...\\nExample Agent Execution Flow:\\nInput: Email (Unimportant offer), message_id_variable = \\\"msg_abc123\\\"\\nAI Reasoning (Internal):\\n(Calls gmail_list_label if needed).\\nAnalyzes email content -> Matches 'Unimportant'.\\nLooks up 'Unimportant' -> Designated ID is Label_14.\\nPrepare tool call: label_id='Label_14', message_id='msg_abc123'.\\nAI Action: Calls gmail_add_label(label_id='Label_14', message_id='msg_abc123').\\n(Assume Tool Call Succeeds)\\nAI Final Output: \\\"Successfully called gmail_add_label to apply Label ID 'Label_14' to message 'msg_abc123'.\\\"\\nHuman: ID: 1981990745b5196c\\nFrom: Cub <<EMAIL>>\\nSubject: Pizza Night Made Easy with Culinary Circle🍕\\nBody: Shop your neighborhood Cub for your faves! To view this email as a web page, go here. culinary circle pizza Cub Instagram Cub Twitter Cub Facebook Cub Pinterest Copyright © 2025 Cub, All rights\"],\"options\":{\"google_api_key\":{\"lc\":1,\"type\":\"secret\",\"id\":[\"GOOGLE_API_KEY\"]},\"base_url\":\"https://generativelanguage.googleapis.com\",\"model\":\"gemini-2.5-flash-preview-05-20\"},\"response\":{\"response\":{\"generations\":[[{\"text\":\"\",\"generationInfo\":{\"finishReason\":\"STOP\",\"index\":0}}]]},\"tokenUsage\":{\"completionTokens\":46,\"promptTokens\":1779,\"totalTokens\":1825}}}"}}
{"__type":"$$EventMessageConfirm","confirm":"01b90337-5be1-4f53-bd66-58e279d3304f","ts":"2025-07-17T14:06:57.553-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"e0b57c34-9034-40e7-893c-a24600e9096e","ts":"2025-07-17T14:06:57.554-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8110","nodeType":"@n8n/n8n-nodes-langchain.mcpClientTool","nodeName":"Gmail Tools","nodeId":"1bf6d8f2-728a-43e7-b1c0-f35da20d0b76"}}
{"__type":"$$EventMessageConfirm","confirm":"e0b57c34-9034-40e7-893c-a24600e9096e","ts":"2025-07-17T14:06:57.554-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageAiNode","id":"67d54992-2b54-4ca0-a9e8-dbd9df62f85b","ts":"2025-07-17T14:06:57.883-04:00","eventName":"n8n.ai.tool.called","message":"n8n.ai.tool.called","payload":{"executionId":"8110","nodeName":"Gmail Tools","workflowName":"Email Labler","nodeType":"@n8n/n8n-nodes-langchain.mcpClientTool","workflowId":"7I3EfxpsDgrByI8X","msg":"{\"query\":{\"Message_ID\":\"1981990745b5196c\",\"Label_Names_or_IDs\":\"Label_14\"},\"tool\":{\"name\":\"Add_Label\",\"description\":\"Add label to message in Gmail\"},\"response\":[{\"type\":\"text\",\"text\":\"[{\\\"id\\\":\\\"1981990745b5196c\\\",\\\"threadId\\\":\\\"1981990745b5196c\\\",\\\"labelIds\\\":[\\\"CATEGORY_PROMOTIONS\\\",\\\"UNREAD\\\",\\\"Label_14\\\",\\\"INBOX\\\"]}]\"}]}"}}
{"__type":"$$EventMessageConfirm","confirm":"67d54992-2b54-4ca0-a9e8-dbd9df62f85b","ts":"2025-07-17T14:06:57.883-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"24c399a0-9546-4cf3-87f1-470982b1c044","ts":"2025-07-17T14:06:57.883-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8110","nodeType":"@n8n/n8n-nodes-langchain.mcpClientTool","nodeName":"Gmail Tools","nodeId":"1bf6d8f2-728a-43e7-b1c0-f35da20d0b76"}}
{"__type":"$$EventMessageConfirm","confirm":"24c399a0-9546-4cf3-87f1-470982b1c044","ts":"2025-07-17T14:06:57.883-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"35e59554-95aa-4cff-a87f-6ef8da6ae8e3","ts":"2025-07-17T14:06:57.894-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8110","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","nodeName":"Google Gemini Chat Model","nodeId":"2431cbe7-32f6-4b11-a623-da84241e6dd2"}}
{"__type":"$$EventMessageConfirm","confirm":"35e59554-95aa-4cff-a87f-6ef8da6ae8e3","ts":"2025-07-17T14:06:57.894-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageWorkflow","id":"0e8c2d83-504a-4d11-a1c1-c463986452da","ts":"2025-07-17T14:06:57.981-04:00","eventName":"n8n.workflow.started","message":"n8n.workflow.started","payload":{"executionId":"8111","workflowId":"7I3EfxpsDgrByI8X","isManual":false,"workflowName":"Email Labler"}}
{"__type":"$$EventMessageConfirm","confirm":"0e8c2d83-504a-4d11-a1c1-c463986452da","ts":"2025-07-17T14:06:57.982-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"80a322a4-4768-4587-9b01-36e462faaebf","ts":"2025-07-17T14:06:57.982-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8111","nodeType":"@n8n/n8n-nodes-langchain.mcpTrigger","nodeName":"MCP Server Trigger","nodeId":"738db1fc-738d-4d2b-9778-697d652bf9d3"}}
{"__type":"$$EventMessageConfirm","confirm":"80a322a4-4768-4587-9b01-36e462faaebf","ts":"2025-07-17T14:06:57.982-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"1db288b1-ebb0-4afd-be5a-b9a85db9b15d","ts":"2025-07-17T14:06:57.982-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8111","nodeType":"@n8n/n8n-nodes-langchain.mcpTrigger","nodeName":"MCP Server Trigger","nodeId":"738db1fc-738d-4d2b-9778-697d652bf9d3"}}
{"__type":"$$EventMessageConfirm","confirm":"1db288b1-ebb0-4afd-be5a-b9a85db9b15d","ts":"2025-07-17T14:06:57.982-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageWorkflow","id":"d4e85336-0860-4cec-8cb0-030033158f3c","ts":"2025-07-17T14:06:57.982-04:00","eventName":"n8n.workflow.success","message":"n8n.workflow.success","payload":{"executionId":"8111","success":true,"isManual":false,"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler"}}
{"__type":"$$EventMessageConfirm","confirm":"d4e85336-0860-4cec-8cb0-030033158f3c","ts":"2025-07-17T14:06:57.982-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"94f3d583-0ea0-44b5-9727-705283b3a086","ts":"2025-07-17T14:06:58.513-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8110","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","nodeName":"Google Gemini Chat Model","nodeId":"2431cbe7-32f6-4b11-a623-da84241e6dd2"}}
{"__type":"$$EventMessageConfirm","confirm":"94f3d583-0ea0-44b5-9727-705283b3a086","ts":"2025-07-17T14:06:58.514-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageAiNode","id":"50e7d667-5157-43c3-9574-50fae6eb92a1","ts":"2025-07-17T14:06:58.514-04:00","eventName":"n8n.ai.llm.generated","message":"n8n.ai.llm.generated","payload":{"executionId":"8110","nodeName":"Google Gemini Chat Model","workflowName":"Email Labler","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","workflowId":"7I3EfxpsDgrByI8X","msg":"{\"messages\":[\"System: You are an AI Email Classification Agent operating within a system that provides access to specific Gmail tools (gmail_list_label, gmail_add_label) via a model context protocol server. Your task is to:\\nDetermine the correct existing Label ID for an incoming email based solely on the definitive list of labels provided below.\\nExecute the labeling by calling the gmail_add_label tool with the determined Label ID and the email's Message ID.\\nInputs:\\nIncoming Email (Sender, Subject, Body)\\nEmail Message ID (Let's assume this is available to you as message_id_variable)\\nProcedural Step: Although the definitive list of labels is provided below, you may need to invoke the gmail_list_label tool as the initial step if required by the system framework. However, for determining the correct label ID, you must strictly adhere to the names and IDs listed in the \\\"Definitive Label List\\\" section below.\\nDefinitive Label List (Source of Truth for Names and IDs):\\nCHAT / CHAT (System Label - Ignore)\\nSENT / SENT (System Label - Ignore)\\nINBOX / INBOX (System Label - Ignore)\\nIMPORTANT / IMPORTANT (System Label - Use ID IMPORTANT for the 'Important' conceptual category)\\nTRASH / TRASH (System Label - Ignore)\\nDRAFT / DRAFT (System Label - Ignore)\\nSPAM / SPAM (System Label - Ignore)\\n... (other system/category labels to ignore for classification) ...\\nInbox Zero / Label_12 (User Label - Ignore)\\nInbox Zero/Acted / Label_13 (User Label - Ignore)\\nUnimportant / Label_14 (User Label - Use for 'Unimportant' category)\\nVerification Codes / Label_15 (User Label - Use for 'Verification Codes' category)\\nApplication Status / Label_16 (User Label - Use for 'Application Status' category)\\nMeeting Confirmed / Label_17 (User Label - Use for 'Meeting Confirmed' category)\\nMessenger Notifications / Label_18 (User Label - Use for 'Messenger Notifications' category)\\nRocket Money Updates / Label_19 (User Label - Use for 'Rocket Money Updates' category)\\nApple Music Analytics / Label_20 (User Label - Use for 'Apple Music Analytics' category)\\nJob Suggestions / Label_21 (User Label - Use for 'Job Suggestions' category)\\nAi-Agent / Label_2878194751042528826 (User Label - Ignore)\\nYoutube Comments / Label_3070697212981345438 (User Label - Use for 'Youtube Comments' category)\\nAi-Agent/Checked / Label_6685525344120202453 (User Label - Ignore)\\nUber Receipts / Label_7198370523552713631 (User Label - Use for 'Uber Receipts' category)\\nAi-Agent/Checked/Unsure / Label_7805829971573720853 (User Label - Ignore)\\n(This list represents all labels currently recognized for classification based on the rules below).\\nClassification and Execution Process:\\n(Procedural Step - If required): Call gmail_list_label.\\nAnalyze Input: Examine the incoming email (Sender, Subject, Body) and note the message_id_variable.\\nMatch Criteria: Compare the email content against the Criteria for the Conceptual Label Names listed below. Apply rules and exclusions strictly.\\nSelect Category: Determine the single most appropriate Conceptual Label Name.\\nIdentify Designated ID: Find the selected Conceptual Label Name in the rules section below and identify its Designated Label ID based on the Definitive Label List above.\\nFallback Logic:\\nIf the email doesn't fit any specific category (1, 2, 5-11), use the 'Important' fallback (Designated ID: IMPORTANT).\\nIf it doesn't fit any specific category or 'Important', and doesn't hit 'Unimportant' exclusions, use the 'Unimportant' fallback (Designated ID: Label_14).\\nIf no category applies even after fallbacks, stop and indicate failure (NO_CATEGORY_MATCH).\\nPrepare Tool Call: You have now determined the target_label_id (the Designated Label ID from step 5 or 6).\\nExecute Labeling Action: Call the gmail_add_label tool. You MUST provide the following parameters:\\nlabel_id: The target_label_id you determined.\\nmessage_id: The message_id_variable provided in the input.\\nFinal Output: Report the outcome of the tool call. For example: \\\"Successfully called gmail_add_label to apply Label ID '[target_label_id]' to message '[message_id_variable]'.\\\" or \\\"Failed to apply label: [Reason from tool call]\\\". Do not just output the Label ID.\\nConceptual Label Names, Criteria, and Designated IDs:\\n(Match email to criteria. Use the corresponding Designated Label ID from the Definitive List above when calling gmail_add_label.)\\nConceptual Label Name: Application Status -> Designated Label ID: Label_16\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Job Suggestions -> Designated Label ID: Label_21\\nCriteria: ... [criteria & exclusions] ...\\nConceptual Label Name: Important -> Designated Label ID: IMPORTANT\\nCriteria: ... [criteria - fallback 1] ...\\nConceptual Label Name: Unimportant -> Designated Label ID: Label_14\\nCriteria: ... [criteria & exclusions - fallback 2] ...\\nConceptual Label Name: Verification Codes -> Designated Label ID: Label_15\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Meeting Confirmed -> Designated Label ID: Label_17\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Apple Music Analytics -> Designated Label ID: Label_20\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Rocket Money Updates -> Designated Label ID: Label_19\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Messenger Notifications -> Designated Label ID: Label_18\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Youtube Comments -> Designated Label ID: Label_3070697212981345438\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Uber Receipts -> Designated Label ID: Label_7198370523552713631\\nCriteria: ... [criteria] ...\\nExample Agent Execution Flow:\\nInput: Email (Unimportant offer), message_id_variable = \\\"msg_abc123\\\"\\nAI Reasoning (Internal):\\n(Calls gmail_list_label if needed).\\nAnalyzes email content -> Matches 'Unimportant'.\\nLooks up 'Unimportant' -> Designated ID is Label_14.\\nPrepare tool call: label_id='Label_14', message_id='msg_abc123'.\\nAI Action: Calls gmail_add_label(label_id='Label_14', message_id='msg_abc123').\\n(Assume Tool Call Succeeds)\\nAI Final Output: \\\"Successfully called gmail_add_label to apply Label ID 'Label_14' to message 'msg_abc123'.\\\"\\nHuman: ID: 1981990745b5196c\\nFrom: Cub <<EMAIL>>\\nSubject: Pizza Night Made Easy with Culinary Circle🍕\\nBody: Shop your neighborhood Cub for your faves! To view this email as a web page, go here. culinary circle pizza Cub Instagram Cub Twitter Cub Facebook Cub Pinterest Copyright © 2025 Cub, All rights\\nAI: model, [\\n  {\\n    \\\"functionCall\\\": {\\n      \\\"name\\\": \\\"Add_Label\\\",\\n      \\\"args\\\": {\\n        \\\"Message_ID\\\": \\\"1981990745b5196c\\\",\\n        \\\"Label_Names_or_IDs\\\": \\\"Label_14\\\"\\n      }\\n    },\\n    \\\"thoughtSignature\\\": \\\"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\\\"\\n  }\\n]\\nTool: [{\\\"type\\\":\\\"text\\\",\\\"text\\\":\\\"[{\\\\\\\"id\\\\\\\":\\\\\\\"1981990745b5196c\\\\\\\",\\\\\\\"threadId\\\\\\\":\\\\\\\"1981990745b5196c\\\\\\\",\\\\\\\"labelIds\\\\\\\":[\\\\\\\"CATEGORY_PROMOTIONS\\\\\\\",\\\\\\\"UNREAD\\\\\\\",\\\\\\\"Label_14\\\\\\\",\\\\\\\"INBOX\\\\\\\"]}]\\\"}]\"],\"options\":{\"google_api_key\":{\"lc\":1,\"type\":\"secret\",\"id\":[\"GOOGLE_API_KEY\"]},\"base_url\":\"https://generativelanguage.googleapis.com\",\"model\":\"gemini-2.5-flash-preview-05-20\"},\"response\":{\"response\":{\"generations\":[[{\"text\":\"Successfully called gmail_add_label to apply Label ID 'Label_14' to message '1981990745b5196c'.\",\"generationInfo\":{\"index\":0,\"finishReason\":\"STOP\"}}]]},\"tokenUsage\":{\"completionTokens\":19,\"promptTokens\":0,\"totalTokens\":19}}}"}}
{"__type":"$$EventMessageConfirm","confirm":"50e7d667-5157-43c3-9574-50fae6eb92a1","ts":"2025-07-17T14:06:58.514-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"9ba3ffec-963b-4e91-978a-10577e56a0ee","ts":"2025-07-17T14:06:58.515-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8110","nodeType":"@n8n/n8n-nodes-langchain.agent","nodeName":"AI Agent","nodeId":"fa2efd94-444c-4efc-8f00-d76b47673a95"}}
{"__type":"$$EventMessageConfirm","confirm":"9ba3ffec-963b-4e91-978a-10577e56a0ee","ts":"2025-07-17T14:06:58.515-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageWorkflow","id":"f4be27e3-33c9-4d07-b0e8-925fb7711ff8","ts":"2025-07-17T14:06:58.516-04:00","eventName":"n8n.workflow.success","message":"n8n.workflow.success","payload":{"executionId":"8110","success":true,"isManual":false,"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler"}}
{"__type":"$$EventMessageConfirm","confirm":"f4be27e3-33c9-4d07-b0e8-925fb7711ff8","ts":"2025-07-17T14:06:58.516-04:00","source":{"id":"0","name":"eventBus"}}
