// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`InputPanel > should render 1`] = `
<div>
  <div
    class="run-data container runData"
    data-test-id="ndv-input-panel"
    data-v-2e5cd75c=""
  >
    <!--v-if-->
    <!--v-if-->
    <div
      class="header"
      data-v-2e5cd75c=""
    >
      <div
        class="title"
        data-v-2e5cd75c=""
      >
        
        <div
          class="titleSection"
        >
          <span
            class="n8n-text text-light size-medium bold title title"
          >
            
            Input
            
          </span>
          <div
            class="n8n-radio-buttons radioGroup"
            data-test-id="input-panel-mode"
            role="radiogroup"
          >
            
            <label
              aria-checked="true"
              class="n8n-radio-button container hoverable"
              role="radio"
              tabindex="-1"
            >
              <div
                class="button active medium"
                data-test-id="radio-button-mapping"
              >
                
                Mapping
                
              </div>
            </label>
            <label
              aria-checked="false"
              class="n8n-radio-button container hoverable"
              role="radio"
              tabindex="-1"
            >
              <div
                class="button medium"
                data-test-id="radio-button-debugging"
              >
                
                From AI
                
              </div>
            </label>
            
          </div>
        </div>
        
      </div>
      <div
        class="displayModes"
        data-test-id="run-data-pane-header"
        data-v-2e5cd75c=""
      >
        <!---->
        <!--v-if-->
        <div
          class="n8n-radio-buttons radioGroup"
          data-test-id="ndv-run-data-display-mode"
          data-v-2e5cd75c=""
          role="radiogroup"
        >
          
          <label
            aria-checked="true"
            class="n8n-radio-button container hoverable"
            role="radio"
            tabindex="-1"
          >
            <div
              class="button active medium"
              data-test-id="radio-button-schema"
            >
              
              Schema
              
            </div>
          </label>
          <label
            aria-checked="false"
            class="n8n-radio-button container hoverable"
            role="radio"
            tabindex="-1"
          >
            <div
              class="button medium"
              data-test-id="radio-button-table"
            >
              
              Table
              
            </div>
          </label>
          <label
            aria-checked="false"
            class="n8n-radio-button container hoverable"
            role="radio"
            tabindex="-1"
          >
            <div
              class="button medium"
              data-test-id="radio-button-json"
            >
              
              JSON
              
            </div>
          </label>
          
        </div>
        <!--v-if-->
        <!--v-if-->
        <div
          class="editModeActions"
          data-v-2e5cd75c=""
          style="display: none;"
        >
          <button
            aria-live="polite"
            class="button button tertiary medium"
            data-v-2e5cd75c=""
          >
            <!--v-if-->
            <span>
              Cancel
            </span>
          </button>
          <button
            aria-live="polite"
            class="button button primary medium ml-2xs ml-2xs"
            data-v-2e5cd75c=""
          >
            <!--v-if-->
            <span>
              Save
            </span>
          </button>
        </div>
      </div>
      <!--v-if-->
    </div>
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
    <!--v-if-->
    
    
    <!--v-if-->
    <div
      class="dataContainer"
      data-test-id="ndv-data-container"
      data-v-2e5cd75c=""
    >
      <!---->
    </div>
    <!--v-if-->
    <transition-stub
      appear="false"
      class="uiBlocker"
      css="true"
      data-v-1a77a378=""
      data-v-2e5cd75c=""
      mode="out-in"
      name="fade"
      persisted="true"
    >
      <div
        aria-hidden="true"
        class="n8n-block-ui uiBlocker"
        data-v-1a77a378=""
        role="dialog"
        style="display: none;"
      />
    </transition-stub>
  </div>
</div>
`;
