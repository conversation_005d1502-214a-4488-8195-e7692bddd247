{"__type":"$$EventMessageWorkflow","id":"049596a1-cfab-4101-afc9-e09933cafb65","ts":"2025-07-17T13:59:53.794-04:00","eventName":"n8n.workflow.started","message":"n8n.workflow.started","payload":{"executionId":"8103","workflowId":"7I3EfxpsDgrByI8X","isManual":false,"workflowName":"Email Labler"}}
{"__type":"$$EventMessageConfirm","confirm":"049596a1-cfab-4101-afc9-e09933cafb65","ts":"2025-07-17T13:59:53.795-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"b506ff8a-8956-49ee-b0ea-057c8f0d9618","ts":"2025-07-17T13:59:53.795-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8103","nodeType":"n8n-nodes-base.gmailTrigger","nodeName":"Gmail Trigger","nodeId":"6738eab9-dd67-4bb1-b0d7-d376f6c65fce"}}
{"__type":"$$EventMessageConfirm","confirm":"b506ff8a-8956-49ee-b0ea-057c8f0d9618","ts":"2025-07-17T13:59:53.795-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"2d80ca89-c2e0-4bcf-9981-cf00d36b8026","ts":"2025-07-17T13:59:53.796-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8103","nodeType":"n8n-nodes-base.gmailTrigger","nodeName":"Gmail Trigger","nodeId":"6738eab9-dd67-4bb1-b0d7-d376f6c65fce"}}
{"__type":"$$EventMessageConfirm","confirm":"2d80ca89-c2e0-4bcf-9981-cf00d36b8026","ts":"2025-07-17T13:59:53.796-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"9996e593-e595-4594-852c-b8aeb3b35efc","ts":"2025-07-17T13:59:53.796-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8103","nodeType":"@n8n/n8n-nodes-langchain.agent","nodeName":"AI Agent","nodeId":"fa2efd94-444c-4efc-8f00-d76b47673a95"}}
{"__type":"$$EventMessageConfirm","confirm":"9996e593-e595-4594-852c-b8aeb3b35efc","ts":"2025-07-17T13:59:53.796-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"1f46b63c-0807-48d7-8723-f5ac851d03e5","ts":"2025-07-17T13:59:55.620-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8103","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","nodeName":"Google Gemini Chat Model","nodeId":"2431cbe7-32f6-4b11-a623-da84241e6dd2"}}
{"__type":"$$EventMessageConfirm","confirm":"1f46b63c-0807-48d7-8723-f5ac851d03e5","ts":"2025-07-17T13:59:55.620-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"8a23b02b-d463-430d-8350-03a5b6e6977a","ts":"2025-07-17T13:59:57.685-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8103","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","nodeName":"Google Gemini Chat Model","nodeId":"2431cbe7-32f6-4b11-a623-da84241e6dd2"}}
{"__type":"$$EventMessageConfirm","confirm":"8a23b02b-d463-430d-8350-03a5b6e6977a","ts":"2025-07-17T13:59:57.685-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageAiNode","id":"966652bb-5f15-403e-9e82-84eeacbe5932","ts":"2025-07-17T13:59:57.686-04:00","eventName":"n8n.ai.llm.generated","message":"n8n.ai.llm.generated","payload":{"executionId":"8103","nodeName":"Google Gemini Chat Model","workflowName":"Email Labler","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","workflowId":"7I3EfxpsDgrByI8X","msg":"{\"messages\":[\"System: You are an AI Email Classification Agent operating within a system that provides access to specific Gmail tools (gmail_list_label, gmail_add_label) via a model context protocol server. Your task is to:\\nDetermine the correct existing Label ID for an incoming email based solely on the definitive list of labels provided below.\\nExecute the labeling by calling the gmail_add_label tool with the determined Label ID and the email's Message ID.\\nInputs:\\nIncoming Email (Sender, Subject, Body)\\nEmail Message ID (Let's assume this is available to you as message_id_variable)\\nProcedural Step: Although the definitive list of labels is provided below, you may need to invoke the gmail_list_label tool as the initial step if required by the system framework. However, for determining the correct label ID, you must strictly adhere to the names and IDs listed in the \\\"Definitive Label List\\\" section below.\\nDefinitive Label List (Source of Truth for Names and IDs):\\nCHAT / CHAT (System Label - Ignore)\\nSENT / SENT (System Label - Ignore)\\nINBOX / INBOX (System Label - Ignore)\\nIMPORTANT / IMPORTANT (System Label - Use ID IMPORTANT for the 'Important' conceptual category)\\nTRASH / TRASH (System Label - Ignore)\\nDRAFT / DRAFT (System Label - Ignore)\\nSPAM / SPAM (System Label - Ignore)\\n... (other system/category labels to ignore for classification) ...\\nInbox Zero / Label_12 (User Label - Ignore)\\nInbox Zero/Acted / Label_13 (User Label - Ignore)\\nUnimportant / Label_14 (User Label - Use for 'Unimportant' category)\\nVerification Codes / Label_15 (User Label - Use for 'Verification Codes' category)\\nApplication Status / Label_16 (User Label - Use for 'Application Status' category)\\nMeeting Confirmed / Label_17 (User Label - Use for 'Meeting Confirmed' category)\\nMessenger Notifications / Label_18 (User Label - Use for 'Messenger Notifications' category)\\nRocket Money Updates / Label_19 (User Label - Use for 'Rocket Money Updates' category)\\nApple Music Analytics / Label_20 (User Label - Use for 'Apple Music Analytics' category)\\nJob Suggestions / Label_21 (User Label - Use for 'Job Suggestions' category)\\nAi-Agent / Label_2878194751042528826 (User Label - Ignore)\\nYoutube Comments / Label_3070697212981345438 (User Label - Use for 'Youtube Comments' category)\\nAi-Agent/Checked / Label_6685525344120202453 (User Label - Ignore)\\nUber Receipts / Label_7198370523552713631 (User Label - Use for 'Uber Receipts' category)\\nAi-Agent/Checked/Unsure / Label_7805829971573720853 (User Label - Ignore)\\n(This list represents all labels currently recognized for classification based on the rules below).\\nClassification and Execution Process:\\n(Procedural Step - If required): Call gmail_list_label.\\nAnalyze Input: Examine the incoming email (Sender, Subject, Body) and note the message_id_variable.\\nMatch Criteria: Compare the email content against the Criteria for the Conceptual Label Names listed below. Apply rules and exclusions strictly.\\nSelect Category: Determine the single most appropriate Conceptual Label Name.\\nIdentify Designated ID: Find the selected Conceptual Label Name in the rules section below and identify its Designated Label ID based on the Definitive Label List above.\\nFallback Logic:\\nIf the email doesn't fit any specific category (1, 2, 5-11), use the 'Important' fallback (Designated ID: IMPORTANT).\\nIf it doesn't fit any specific category or 'Important', and doesn't hit 'Unimportant' exclusions, use the 'Unimportant' fallback (Designated ID: Label_14).\\nIf no category applies even after fallbacks, stop and indicate failure (NO_CATEGORY_MATCH).\\nPrepare Tool Call: You have now determined the target_label_id (the Designated Label ID from step 5 or 6).\\nExecute Labeling Action: Call the gmail_add_label tool. You MUST provide the following parameters:\\nlabel_id: The target_label_id you determined.\\nmessage_id: The message_id_variable provided in the input.\\nFinal Output: Report the outcome of the tool call. For example: \\\"Successfully called gmail_add_label to apply Label ID '[target_label_id]' to message '[message_id_variable]'.\\\" or \\\"Failed to apply label: [Reason from tool call]\\\". Do not just output the Label ID.\\nConceptual Label Names, Criteria, and Designated IDs:\\n(Match email to criteria. Use the corresponding Designated Label ID from the Definitive List above when calling gmail_add_label.)\\nConceptual Label Name: Application Status -> Designated Label ID: Label_16\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Job Suggestions -> Designated Label ID: Label_21\\nCriteria: ... [criteria & exclusions] ...\\nConceptual Label Name: Important -> Designated Label ID: IMPORTANT\\nCriteria: ... [criteria - fallback 1] ...\\nConceptual Label Name: Unimportant -> Designated Label ID: Label_14\\nCriteria: ... [criteria & exclusions - fallback 2] ...\\nConceptual Label Name: Verification Codes -> Designated Label ID: Label_15\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Meeting Confirmed -> Designated Label ID: Label_17\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Apple Music Analytics -> Designated Label ID: Label_20\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Rocket Money Updates -> Designated Label ID: Label_19\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Messenger Notifications -> Designated Label ID: Label_18\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Youtube Comments -> Designated Label ID: Label_3070697212981345438\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Uber Receipts -> Designated Label ID: Label_7198370523552713631\\nCriteria: ... [criteria] ...\\nExample Agent Execution Flow:\\nInput: Email (Unimportant offer), message_id_variable = \\\"msg_abc123\\\"\\nAI Reasoning (Internal):\\n(Calls gmail_list_label if needed).\\nAnalyzes email content -> Matches 'Unimportant'.\\nLooks up 'Unimportant' -> Designated ID is Label_14.\\nPrepare tool call: label_id='Label_14', message_id='msg_abc123'.\\nAI Action: Calls gmail_add_label(label_id='Label_14', message_id='msg_abc123').\\n(Assume Tool Call Succeeds)\\nAI Final Output: \\\"Successfully called gmail_add_label to apply Label ID 'Label_14' to message 'msg_abc123'.\\\"\\nHuman: ID: 198198ab515315e7\\nFrom: Indeed <<EMAIL>>\\nSubject: Host @ Areas USA, Inc\\nBody: $20.10 an hour. Hi jalen, It looks like your background could be a match for this Host role. Please submit a quick application if you have any interest. ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌\"],\"options\":{\"google_api_key\":{\"lc\":1,\"type\":\"secret\",\"id\":[\"GOOGLE_API_KEY\"]},\"base_url\":\"https://generativelanguage.googleapis.com\",\"model\":\"gemini-2.5-flash-preview-05-20\"},\"response\":{\"response\":{\"generations\":[[{\"text\":\"\",\"generationInfo\":{\"finishReason\":\"STOP\",\"index\":0}}]]},\"tokenUsage\":{\"completionTokens\":45,\"promptTokens\":1821,\"totalTokens\":1866}}}"}}
{"__type":"$$EventMessageConfirm","confirm":"966652bb-5f15-403e-9e82-84eeacbe5932","ts":"2025-07-17T13:59:57.686-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"31fd993c-bd63-4155-97ea-132668036b58","ts":"2025-07-17T13:59:57.694-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8103","nodeType":"@n8n/n8n-nodes-langchain.mcpClientTool","nodeName":"Gmail Tools","nodeId":"1bf6d8f2-728a-43e7-b1c0-f35da20d0b76"}}
{"__type":"$$EventMessageConfirm","confirm":"31fd993c-bd63-4155-97ea-132668036b58","ts":"2025-07-17T13:59:57.695-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageAiNode","id":"4856b116-55b2-4907-b041-31aa33c6df7f","ts":"2025-07-17T13:59:58.131-04:00","eventName":"n8n.ai.tool.called","message":"n8n.ai.tool.called","payload":{"executionId":"8103","nodeName":"Gmail Tools","workflowName":"Email Labler","nodeType":"@n8n/n8n-nodes-langchain.mcpClientTool","workflowId":"7I3EfxpsDgrByI8X","msg":"{\"query\":{\"Message_ID\":\"198198ab515315e7\",\"Label_Names_or_IDs\":\"Label_21\"},\"tool\":{\"name\":\"Add_Label\",\"description\":\"Add label to message in Gmail\"},\"response\":[{\"type\":\"text\",\"text\":\"[{\\\"id\\\":\\\"198198ab515315e7\\\",\\\"threadId\\\":\\\"198198ab515315e7\\\",\\\"labelIds\\\":[\\\"UNREAD\\\",\\\"Label_21\\\",\\\"CATEGORY_UPDATES\\\",\\\"INBOX\\\"]}]\"}]}"}}
{"__type":"$$EventMessageConfirm","confirm":"4856b116-55b2-4907-b041-31aa33c6df7f","ts":"2025-07-17T13:59:58.131-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"2d31c1bb-e042-4410-a519-c85f649fb8a5","ts":"2025-07-17T13:59:58.131-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8103","nodeType":"@n8n/n8n-nodes-langchain.mcpClientTool","nodeName":"Gmail Tools","nodeId":"1bf6d8f2-728a-43e7-b1c0-f35da20d0b76"}}
{"__type":"$$EventMessageConfirm","confirm":"2d31c1bb-e042-4410-a519-c85f649fb8a5","ts":"2025-07-17T13:59:58.131-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"1fc1b58f-351e-4487-ba87-ab2f9db1e38e","ts":"2025-07-17T13:59:58.145-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8103","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","nodeName":"Google Gemini Chat Model","nodeId":"2431cbe7-32f6-4b11-a623-da84241e6dd2"}}
{"__type":"$$EventMessageConfirm","confirm":"1fc1b58f-351e-4487-ba87-ab2f9db1e38e","ts":"2025-07-17T13:59:58.145-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageWorkflow","id":"edfa666b-9c63-4622-8f22-ec2204f9b662","ts":"2025-07-17T13:59:58.222-04:00","eventName":"n8n.workflow.started","message":"n8n.workflow.started","payload":{"executionId":"8104","workflowId":"7I3EfxpsDgrByI8X","isManual":false,"workflowName":"Email Labler"}}
{"__type":"$$EventMessageConfirm","confirm":"edfa666b-9c63-4622-8f22-ec2204f9b662","ts":"2025-07-17T13:59:58.222-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"58a1e29e-da8d-4d38-a9e4-2d536caa6245","ts":"2025-07-17T13:59:58.222-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8104","nodeType":"@n8n/n8n-nodes-langchain.mcpTrigger","nodeName":"MCP Server Trigger","nodeId":"738db1fc-738d-4d2b-9778-697d652bf9d3"}}
{"__type":"$$EventMessageConfirm","confirm":"58a1e29e-da8d-4d38-a9e4-2d536caa6245","ts":"2025-07-17T13:59:58.222-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"11ccaf05-14d9-453d-af11-144d927c5073","ts":"2025-07-17T13:59:58.222-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8104","nodeType":"@n8n/n8n-nodes-langchain.mcpTrigger","nodeName":"MCP Server Trigger","nodeId":"738db1fc-738d-4d2b-9778-697d652bf9d3"}}
{"__type":"$$EventMessageConfirm","confirm":"11ccaf05-14d9-453d-af11-144d927c5073","ts":"2025-07-17T13:59:58.222-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageWorkflow","id":"b2695e26-ea4b-4272-bc09-017622b31669","ts":"2025-07-17T13:59:58.223-04:00","eventName":"n8n.workflow.success","message":"n8n.workflow.success","payload":{"executionId":"8104","success":true,"isManual":false,"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler"}}
{"__type":"$$EventMessageConfirm","confirm":"b2695e26-ea4b-4272-bc09-017622b31669","ts":"2025-07-17T13:59:58.223-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"d19b85bc-5cca-425f-a6d8-0ab5c4b865d7","ts":"2025-07-17T13:59:59.758-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8103","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","nodeName":"Google Gemini Chat Model","nodeId":"2431cbe7-32f6-4b11-a623-da84241e6dd2"}}
{"__type":"$$EventMessageConfirm","confirm":"d19b85bc-5cca-425f-a6d8-0ab5c4b865d7","ts":"2025-07-17T13:59:59.758-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageAiNode","id":"183dab73-d74a-4e27-a9d9-3bc77c5270e8","ts":"2025-07-17T13:59:59.758-04:00","eventName":"n8n.ai.llm.generated","message":"n8n.ai.llm.generated","payload":{"executionId":"8103","nodeName":"Google Gemini Chat Model","workflowName":"Email Labler","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","workflowId":"7I3EfxpsDgrByI8X","msg":"{\"messages\":[\"System: You are an AI Email Classification Agent operating within a system that provides access to specific Gmail tools (gmail_list_label, gmail_add_label) via a model context protocol server. Your task is to:\\nDetermine the correct existing Label ID for an incoming email based solely on the definitive list of labels provided below.\\nExecute the labeling by calling the gmail_add_label tool with the determined Label ID and the email's Message ID.\\nInputs:\\nIncoming Email (Sender, Subject, Body)\\nEmail Message ID (Let's assume this is available to you as message_id_variable)\\nProcedural Step: Although the definitive list of labels is provided below, you may need to invoke the gmail_list_label tool as the initial step if required by the system framework. However, for determining the correct label ID, you must strictly adhere to the names and IDs listed in the \\\"Definitive Label List\\\" section below.\\nDefinitive Label List (Source of Truth for Names and IDs):\\nCHAT / CHAT (System Label - Ignore)\\nSENT / SENT (System Label - Ignore)\\nINBOX / INBOX (System Label - Ignore)\\nIMPORTANT / IMPORTANT (System Label - Use ID IMPORTANT for the 'Important' conceptual category)\\nTRASH / TRASH (System Label - Ignore)\\nDRAFT / DRAFT (System Label - Ignore)\\nSPAM / SPAM (System Label - Ignore)\\n... (other system/category labels to ignore for classification) ...\\nInbox Zero / Label_12 (User Label - Ignore)\\nInbox Zero/Acted / Label_13 (User Label - Ignore)\\nUnimportant / Label_14 (User Label - Use for 'Unimportant' category)\\nVerification Codes / Label_15 (User Label - Use for 'Verification Codes' category)\\nApplication Status / Label_16 (User Label - Use for 'Application Status' category)\\nMeeting Confirmed / Label_17 (User Label - Use for 'Meeting Confirmed' category)\\nMessenger Notifications / Label_18 (User Label - Use for 'Messenger Notifications' category)\\nRocket Money Updates / Label_19 (User Label - Use for 'Rocket Money Updates' category)\\nApple Music Analytics / Label_20 (User Label - Use for 'Apple Music Analytics' category)\\nJob Suggestions / Label_21 (User Label - Use for 'Job Suggestions' category)\\nAi-Agent / Label_2878194751042528826 (User Label - Ignore)\\nYoutube Comments / Label_3070697212981345438 (User Label - Use for 'Youtube Comments' category)\\nAi-Agent/Checked / Label_6685525344120202453 (User Label - Ignore)\\nUber Receipts / Label_7198370523552713631 (User Label - Use for 'Uber Receipts' category)\\nAi-Agent/Checked/Unsure / Label_7805829971573720853 (User Label - Ignore)\\n(This list represents all labels currently recognized for classification based on the rules below).\\nClassification and Execution Process:\\n(Procedural Step - If required): Call gmail_list_label.\\nAnalyze Input: Examine the incoming email (Sender, Subject, Body) and note the message_id_variable.\\nMatch Criteria: Compare the email content against the Criteria for the Conceptual Label Names listed below. Apply rules and exclusions strictly.\\nSelect Category: Determine the single most appropriate Conceptual Label Name.\\nIdentify Designated ID: Find the selected Conceptual Label Name in the rules section below and identify its Designated Label ID based on the Definitive Label List above.\\nFallback Logic:\\nIf the email doesn't fit any specific category (1, 2, 5-11), use the 'Important' fallback (Designated ID: IMPORTANT).\\nIf it doesn't fit any specific category or 'Important', and doesn't hit 'Unimportant' exclusions, use the 'Unimportant' fallback (Designated ID: Label_14).\\nIf no category applies even after fallbacks, stop and indicate failure (NO_CATEGORY_MATCH).\\nPrepare Tool Call: You have now determined the target_label_id (the Designated Label ID from step 5 or 6).\\nExecute Labeling Action: Call the gmail_add_label tool. You MUST provide the following parameters:\\nlabel_id: The target_label_id you determined.\\nmessage_id: The message_id_variable provided in the input.\\nFinal Output: Report the outcome of the tool call. For example: \\\"Successfully called gmail_add_label to apply Label ID '[target_label_id]' to message '[message_id_variable]'.\\\" or \\\"Failed to apply label: [Reason from tool call]\\\". Do not just output the Label ID.\\nConceptual Label Names, Criteria, and Designated IDs:\\n(Match email to criteria. Use the corresponding Designated Label ID from the Definitive List above when calling gmail_add_label.)\\nConceptual Label Name: Application Status -> Designated Label ID: Label_16\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Job Suggestions -> Designated Label ID: Label_21\\nCriteria: ... [criteria & exclusions] ...\\nConceptual Label Name: Important -> Designated Label ID: IMPORTANT\\nCriteria: ... [criteria - fallback 1] ...\\nConceptual Label Name: Unimportant -> Designated Label ID: Label_14\\nCriteria: ... [criteria & exclusions - fallback 2] ...\\nConceptual Label Name: Verification Codes -> Designated Label ID: Label_15\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Meeting Confirmed -> Designated Label ID: Label_17\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Apple Music Analytics -> Designated Label ID: Label_20\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Rocket Money Updates -> Designated Label ID: Label_19\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Messenger Notifications -> Designated Label ID: Label_18\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Youtube Comments -> Designated Label ID: Label_3070697212981345438\\nCriteria: ... [criteria] ...\\nConceptual Label Name: Uber Receipts -> Designated Label ID: Label_7198370523552713631\\nCriteria: ... [criteria] ...\\nExample Agent Execution Flow:\\nInput: Email (Unimportant offer), message_id_variable = \\\"msg_abc123\\\"\\nAI Reasoning (Internal):\\n(Calls gmail_list_label if needed).\\nAnalyzes email content -> Matches 'Unimportant'.\\nLooks up 'Unimportant' -> Designated ID is Label_14.\\nPrepare tool call: label_id='Label_14', message_id='msg_abc123'.\\nAI Action: Calls gmail_add_label(label_id='Label_14', message_id='msg_abc123').\\n(Assume Tool Call Succeeds)\\nAI Final Output: \\\"Successfully called gmail_add_label to apply Label ID 'Label_14' to message 'msg_abc123'.\\\"\\nHuman: ID: 198198ab515315e7\\nFrom: Indeed <<EMAIL>>\\nSubject: Host @ Areas USA, Inc\\nBody: $20.10 an hour. Hi jalen, It looks like your background could be a match for this Host role. Please submit a quick application if you have any interest. ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌\\nAI: model, [\\n  {\\n    \\\"functionCall\\\": {\\n      \\\"name\\\": \\\"Add_Label\\\",\\n      \\\"args\\\": {\\n        \\\"Label_Names_or_IDs\\\": \\\"Label_21\\\",\\n        \\\"Message_ID\\\": \\\"198198ab515315e7\\\"\\n      }\\n    },\\n    \\\"thoughtSignature\\\": \\\"Ci0BVKhc7uKN4WlvgqIn4L8CSSO1xR4ytfj0l7qcVpZBgRPxZEpVtA/A8ww/Z/4KbAFUqFzusHaWzGMstTvHIL8KtPc1mv19zyD996awMSMb26WXHSW6di74d2c3/O+8qFjf52WdBEacE3I9ej7bFW0p/T4fYu+GmvlL3O8NmlXUfVfdhOHPdQQ16rkjpIyUdSRP06lrID1mDZn14Qr5AQFUqFzuT2y9po2ibsjS1KFOyH3q7TXxteBzDxm5vGEUxJDpOmNvXxkvTee6pT+ryArJFjzRcG+CwGhKY7/6YOCEUyxFj2ge7tv1Dzh+MDCvUm65mWHNaa2rgfv8TUY3RNU4/ReEO30CwohIQ5aFNCdXQfMhxHmjg18yuYw/J+XFcxRIfMo6K3J2d+CxGHSYYtPp+n9eG3Mf+xPUtpg4X5zXn7nQ3gKlsygydaqztOITQrinl9qDLpa+Fu6fvy0AlL6gjuw+fxdgsIMaj2A596wTl+jH/eezqRIwDNMa3nZ1GTjg2EwnMXbU3bHoL34MFpekLKpXxSAjCAq8AQFUqFzux1tnFEB4blx5QVoKCz0EfPYJF3dxVGtkIsPjw4Q8TciIPG25Dtj5tt8t+IviXiYBAHPbpvNvUfP35NsyFxPpLSOkVbRbXTD244ESDglaE6RaISIvTLGs7jmF1yD8GEdTQrF7Pd1aiTk0MDl0rbKzPyQj8K4rAfPTXJA7KKobdb9nQvSKqVMkEla8NaEKzj4oo7FCKyOFXsn4Xgd0WNhcdblHGQaWNo9h6dyRk8WN6WK87SC5zp+FCt0BAVSoXO4OcpC9v3LvQaDFUiIxI8sqHj40cxfp28Cmt1s5epB1jD2C8+eP1WhbXW4pXai9cxsMXaeiLWpVbpXORs0AZWLReL9OPl5CXjkyadFLl+cQz98T3YiwvQ3Y0am5y9fReyIVEfcKykhi/gQfos7uhoUM8obmJCPvJW4kxo8VHH7QfHfmifdCfl/52eSlspV3PqoJ5ocY19vlzJ1XFmhzf2nCL4e04ybLc1UHU8LEi6x/L90lAlU0gkO4yMyztIvM0cWg+T0zgwOEHSfehvnA7e3ErSnEVCP4HPwKhwIBVKhc7pozyz7rfth8Mg/bKv4R2vFWw/f11jD4B7LAY2JQV6+puKloWxnLwDHH/6QUBU443cq4gUMtrfRJncRnHS51G/uIiqzhNWKNXtDdM+v7iNNd9rpqKDT8JQRG7ce51eJU0rbCeMXnxC6CDhwFj5NNRlWHBGAEiBkImZbF/iVX73WqIYN6dHMzDPthBH6aAFvNetpn/RNF5nk0Ble6ZDTry6jsx/KntAcwctsZbm1uVwAuUhXFkZUGWaBdnC2qvEKVt2Jl3pztnFLloMHLDOIQ3WC/xEqxRkoWgTh6oNWS2KatKCy1P51H9OzfgIKQ0HVptFTVTWo/Ubet2jKd1N83C2XhtAqoAQFUqFzunoGt0NtZHpDe/L6aYFWOdNVLN6ynHItKlEi8aFCsoFU+IUmer9lUjNAUTqWcPKS2YDCvQdPO8lel3m96ePPcxvC/7cUgWuQKILDOEXXzXClI7XtcyF068seEKovFe3Cku1+KNViNpr9g8Toa1VEZnUIQs8gW5RX7jRaL3bw4ljTfspBfjD34o9iV5APvo6y3lFbK8JU4TtQM9teL3Ox77WmkkQp0AVSoXO54N5QpB+eWqyuJ41hTbomySEBRfOXxgNsecGFPpMAGzeyu+d93R799BzKipWAXGIhwClZ95TJwrOi9j/U8bXNUOhfCUiX4RaFAU9r/Uk+ubcEXAW/9IhzKIGck1ujbAZh+cqFj2hGtyZQ84Wo46aA=\\\"\\n  }\\n]\\nTool: [{\\\"type\\\":\\\"text\\\",\\\"text\\\":\\\"[{\\\\\\\"id\\\\\\\":\\\\\\\"198198ab515315e7\\\\\\\",\\\\\\\"threadId\\\\\\\":\\\\\\\"198198ab515315e7\\\\\\\",\\\\\\\"labelIds\\\\\\\":[\\\\\\\"UNREAD\\\\\\\",\\\\\\\"Label_21\\\\\\\",\\\\\\\"CATEGORY_UPDATES\\\\\\\",\\\\\\\"INBOX\\\\\\\"]}]\\\"}]\"],\"options\":{\"google_api_key\":{\"lc\":1,\"type\":\"secret\",\"id\":[\"GOOGLE_API_KEY\"]},\"base_url\":\"https://generativelanguage.googleapis.com\",\"model\":\"gemini-2.5-flash-preview-05-20\"},\"response\":{\"response\":{\"generations\":[[{\"text\":\"Successfully called gmail_add_label to apply Label ID 'Label_21' to message '198198ab515315e7'.\",\"generationInfo\":{\"index\":0,\"finishReason\":\"STOP\"}}]]},\"tokenUsageEstimate\":{\"completionTokens\":25,\"promptTokens\":2872,\"totalTokens\":2897}}}"}}
{"__type":"$$EventMessageConfirm","confirm":"183dab73-d74a-4e27-a9d9-3bc77c5270e8","ts":"2025-07-17T13:59:59.758-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"5a048407-f817-4e0d-988a-b7f5ae2e4b4f","ts":"2025-07-17T13:59:59.778-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler","executionId":"8103","nodeType":"@n8n/n8n-nodes-langchain.agent","nodeName":"AI Agent","nodeId":"fa2efd94-444c-4efc-8f00-d76b47673a95"}}
{"__type":"$$EventMessageConfirm","confirm":"5a048407-f817-4e0d-988a-b7f5ae2e4b4f","ts":"2025-07-17T13:59:59.778-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageWorkflow","id":"0807e90c-1d22-47f8-bf0f-64903c9687f9","ts":"2025-07-17T13:59:59.778-04:00","eventName":"n8n.workflow.success","message":"n8n.workflow.success","payload":{"executionId":"8103","success":true,"isManual":false,"workflowId":"7I3EfxpsDgrByI8X","workflowName":"Email Labler"}}
{"__type":"$$EventMessageConfirm","confirm":"0807e90c-1d22-47f8-bf0f-64903c9687f9","ts":"2025-07-17T13:59:59.778-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageWorkflow","id":"45a21316-0802-43ec-843a-708a8fea65b4","ts":"2025-07-17T14:00:28.004-04:00","eventName":"n8n.workflow.started","message":"n8n.workflow.started","payload":{"executionId":"8105","workflowId":"l5tiEZ9mwZAvOjTN","isManual":false,"workflowName":"Gmail trigger Agent"}}
{"__type":"$$EventMessageConfirm","confirm":"45a21316-0802-43ec-843a-708a8fea65b4","ts":"2025-07-17T14:00:28.004-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"65d36487-b17f-41bb-bb0b-8f81c925ede4","ts":"2025-07-17T14:00:28.005-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"l5tiEZ9mwZAvOjTN","workflowName":"Gmail trigger Agent","executionId":"8105","nodeType":"n8n-nodes-base.gmailTrigger","nodeName":"Gmail Trigger","nodeId":"a67441ca-d29f-49b3-bdc9-591d79ea421e"}}
{"__type":"$$EventMessageConfirm","confirm":"65d36487-b17f-41bb-bb0b-8f81c925ede4","ts":"2025-07-17T14:00:28.005-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"cf0708f5-4610-4df7-b66a-0877908aa686","ts":"2025-07-17T14:00:28.005-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"l5tiEZ9mwZAvOjTN","workflowName":"Gmail trigger Agent","executionId":"8105","nodeType":"n8n-nodes-base.gmailTrigger","nodeName":"Gmail Trigger","nodeId":"a67441ca-d29f-49b3-bdc9-591d79ea421e"}}
{"__type":"$$EventMessageConfirm","confirm":"cf0708f5-4610-4df7-b66a-0877908aa686","ts":"2025-07-17T14:00:28.005-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"795f9eea-59da-4bb8-939c-da55bd6303d9","ts":"2025-07-17T14:00:28.006-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"l5tiEZ9mwZAvOjTN","workflowName":"Gmail trigger Agent","executionId":"8105","nodeType":"@n8n/n8n-nodes-langchain.agent","nodeName":"AI Agent","nodeId":"65a6d9ea-00c8-40a2-a8a1-f2cf94129e1a"}}
{"__type":"$$EventMessageConfirm","confirm":"795f9eea-59da-4bb8-939c-da55bd6303d9","ts":"2025-07-17T14:00:28.006-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"34ad9ced-4006-4a0b-8c93-82b8af42ed04","ts":"2025-07-17T14:00:28.053-04:00","eventName":"n8n.node.started","message":"n8n.node.started","payload":{"workflowId":"l5tiEZ9mwZAvOjTN","workflowName":"Gmail trigger Agent","executionId":"8105","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","nodeName":"Google Gemini Chat Model","nodeId":"7db1c3df-665d-4861-9248-640ebe1324a9"}}
{"__type":"$$EventMessageConfirm","confirm":"34ad9ced-4006-4a0b-8c93-82b8af42ed04","ts":"2025-07-17T14:00:28.053-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"6de5dcd4-5f14-411f-8a56-64f71ea05336","ts":"2025-07-17T14:00:30.945-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"l5tiEZ9mwZAvOjTN","workflowName":"Gmail trigger Agent","executionId":"8105","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","nodeName":"Google Gemini Chat Model","nodeId":"7db1c3df-665d-4861-9248-640ebe1324a9"}}
{"__type":"$$EventMessageConfirm","confirm":"6de5dcd4-5f14-411f-8a56-64f71ea05336","ts":"2025-07-17T14:00:30.945-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageAiNode","id":"2941b312-6d36-4d6b-b1d5-5b12ee0743c0","ts":"2025-07-17T14:00:30.945-04:00","eventName":"n8n.ai.llm.generated","message":"n8n.ai.llm.generated","payload":{"executionId":"8105","nodeName":"Google Gemini Chat Model","workflowName":"Gmail trigger Agent","nodeType":"@n8n/n8n-nodes-langchain.lmChatGoogleGemini","workflowId":"l5tiEZ9mwZAvOjTN","msg":"{\"messages\":[\"System: You are an intelligent mailing assistant. Your role is to read structured email data and provide a clear, helpful summary for the user. Each email will be given in this format:\\n\\nmakefile\\nCopy\\nEdit\\nFrom: [sender name and email]\\nTO: [recipient email]\\nSubject: [email subject]\\nbody: [email message body]\\nlabels: [comma-separated label names]\\nYour job is to:\\n\\nSummarize the email clearly in one or two sentences.\\n\\nIdentify the email type: one of the following — personal, notification, promotion, transactional, or spam.\\n\\nSuggest a next action for the user, such as reply, mark as read, ignore, or archive.\\n\\nUse the labels to help infer context or priority.\\nHuman: From:  Indeed <<EMAIL>>\\nTO: <<EMAIL>>\\nSubject: Host @ Areas USA, Inc\\nbody: $20.10 an hour. Hi jalen, It looks like your background could be a match for this Host role. Please submit a quick application if you have any interest. ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌ ͏ ‌\\nlbales: INBOX, CATEGORY_UPDATES, UNREAD, Job Suggestions\"],\"options\":{\"google_api_key\":{\"lc\":1,\"type\":\"secret\",\"id\":[\"GOOGLE_API_KEY\"]},\"base_url\":\"https://generativelanguage.googleapis.com\",\"model\":\"gemini-2.5-flash-preview-05-20\"},\"response\":{\"response\":{\"generations\":[[{\"text\":\"Here's a summary of the email:\\n\\nThis email from Indeed suggests a Host position at Areas USA, Inc. for $20.10 an hour, encouraging you to submit an application if interested.\\n\\nEmail Type: Notification\\n\\nNext Action: Apply if interested, or archive.\",\"generationInfo\":{\"index\":0,\"finishReason\":\"STOP\"}}]]},\"tokenUsage\":{\"completionTokens\":14,\"promptTokens\":0,\"totalTokens\":14}}}"}}
{"__type":"$$EventMessageConfirm","confirm":"2941b312-6d36-4d6b-b1d5-5b12ee0743c0","ts":"2025-07-17T14:00:30.945-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageNode","id":"641b37f5-afa5-453d-8df1-2ae36031f1d8","ts":"2025-07-17T14:00:30.948-04:00","eventName":"n8n.node.finished","message":"n8n.node.finished","payload":{"workflowId":"l5tiEZ9mwZAvOjTN","workflowName":"Gmail trigger Agent","executionId":"8105","nodeType":"@n8n/n8n-nodes-langchain.agent","nodeName":"AI Agent","nodeId":"65a6d9ea-00c8-40a2-a8a1-f2cf94129e1a"}}
{"__type":"$$EventMessageConfirm","confirm":"641b37f5-afa5-453d-8df1-2ae36031f1d8","ts":"2025-07-17T14:00:30.948-04:00","source":{"id":"0","name":"eventBus"}}
{"__type":"$$EventMessageWorkflow","id":"0df8a52f-39aa-459c-9348-1a332c7717bf","ts":"2025-07-17T14:00:30.950-04:00","eventName":"n8n.workflow.success","message":"n8n.workflow.success","payload":{"executionId":"8105","success":true,"isManual":false,"workflowId":"l5tiEZ9mwZAvOjTN","workflowName":"Gmail trigger Agent"}}
{"__type":"$$EventMessageConfirm","confirm":"0df8a52f-39aa-459c-9348-1a332c7717bf","ts":"2025-07-17T14:00:30.950-04:00","source":{"id":"0","name":"eventBus"}}
