{"name": "@n8n/n8n-nodes-langchain", "version": "1.102.0", "description": "", "main": "index.js", "scripts": {"clean": "rimraf dist .turbo", "dev": "pnpm run watch", "typecheck": "tsc --noEmit", "copy-nodes-json": "node ../../nodes-base/scripts/copy-nodes-json.js .", "copy-tokenizer-json": "node scripts/copy-tokenizer-json.js .", "build": "tsup --tsconfig tsconfig.build.json && pnpm copy-nodes-json && tsc-alias -p tsconfig.build.json && pnpm copy-tokenizer-json && pnpm n8n-copy-static-files && pnpm n8n-generate-metadata", "format": "biome format --write .", "format:check": "biome ci .", "lint": "eslint nodes credentials utils --quiet", "lintfix": "eslint nodes credentials utils --fix", "watch": "tsup --watch nodes --watch credentials --watch utils --watch types --tsconfig tsconfig.build.json --onSuccess \"node ./scripts/post-build.js\"", "test": "jest", "test:dev": "jest --watch"}, "files": ["dist"], "n8n": {"n8nNodesApiVersion": 1, "credentials": ["dist/credentials/AnthropicApi.credentials.js", "dist/credentials/AzureOpenAiApi.credentials.js", "dist/credentials/AzureEntraCognitiveServicesOAuth2Api.credentials.js", "dist/credentials/CohereApi.credentials.js", "dist/credentials/DeepSeekApi.credentials.js", "dist/credentials/GooglePalmApi.credentials.js", "dist/credentials/GroqApi.credentials.js", "dist/credentials/HuggingFaceApi.credentials.js", "dist/credentials/MotorheadApi.credentials.js", "dist/credentials/MilvusApi.credentials.js", "dist/credentials/MistralCloudApi.credentials.js", "dist/credentials/OllamaApi.credentials.js", "dist/credentials/OpenRouterApi.credentials.js", "dist/credentials/PineconeApi.credentials.js", "dist/credentials/QdrantApi.credentials.js", "dist/credentials/SearXngApi.credentials.js", "dist/credentials/SerpApi.credentials.js", "dist/credentials/WeaviateApi.credentials.js", "dist/credentials/WolframAlphaApi.credentials.js", "dist/credentials/XAiApi.credentials.js", "dist/credentials/XataApi.credentials.js", "dist/credentials/ZepApi.credentials.js"], "nodes": ["dist/nodes/vendors/GoogleGemini/GoogleGemini.node.js", "dist/nodes/vendors/OpenAi/OpenAi.node.js", "dist/nodes/agents/Agent/Agent.node.js", "dist/nodes/agents/Agent/AgentTool.node.js", "dist/nodes/agents/OpenAiAssistant/OpenAiAssistant.node.js", "dist/nodes/chains/ChainSummarization/ChainSummarization.node.js", "dist/nodes/chains/ChainLLM/ChainLlm.node.js", "dist/nodes/chains/ChainRetrievalQA/ChainRetrievalQa.node.js", "dist/nodes/chains/SentimentAnalysis/SentimentAnalysis.node.js", "dist/nodes/chains/InformationExtractor/InformationExtractor.node.js", "dist/nodes/chains/TextClassifier/TextClassifier.node.js", "dist/nodes/code/Code.node.js", "dist/nodes/document_loaders/DocumentDefaultDataLoader/DocumentDefaultDataLoader.node.js", "dist/nodes/document_loaders/DocumentBinaryInputLoader/DocumentBinaryInputLoader.node.js", "dist/nodes/document_loaders/DocumentGithubLoader/DocumentGithubLoader.node.js", "dist/nodes/document_loaders/DocumentJSONInputLoader/DocumentJsonInputLoader.node.js", "dist/nodes/embeddings/EmbeddingsCohere/EmbeddingsCohere.node.js", "dist/nodes/embeddings/EmbeddingsAwsBedrock/EmbeddingsAwsBedrock.node.js", "dist/nodes/embeddings/EmbeddingsAzureOpenAi/EmbeddingsAzureOpenAi.node.js", "dist/nodes/embeddings/EmbeddingsGoogleGemini/EmbeddingsGoogleGemini.node.js", "dist/nodes/embeddings/EmbeddingsGoogleVertex/EmbeddingsGoogleVertex.node.js", "dist/nodes/embeddings/EmbeddingsHuggingFaceInference/EmbeddingsHuggingFaceInference.node.js", "dist/nodes/embeddings/EmbeddingsMistralCloud/EmbeddingsMistralCloud.node.js", "dist/nodes/embeddings/EmbeddingsOpenAI/EmbeddingsOpenAi.node.js", "dist/nodes/embeddings/EmbeddingsOllama/EmbeddingsOllama.node.js", "dist/nodes/llms/LMChatAnthropic/LmChatAnthropic.node.js", "dist/nodes/llms/LmChatAzureOpenAi/LmChatAzureOpenAi.node.js", "dist/nodes/llms/LmChatAwsBedrock/LmChatAwsBedrock.node.js", "dist/nodes/llms/LmChatCohere/LmChatCohere.node.js", "dist/nodes/llms/LmChatDeepSeek/LmChatDeepSeek.node.js", "dist/nodes/llms/LmChatGoogleGemini/LmChatGoogleGemini.node.js", "dist/nodes/llms/LmChatGoogleVertex/LmChatGoogleVertex.node.js", "dist/nodes/llms/LmChatGroq/LmChatGroq.node.js", "dist/nodes/llms/LmChatMistralCloud/LmChatMistralCloud.node.js", "dist/nodes/llms/LMChatOllama/LmChatOllama.node.js", "dist/nodes/llms/LmChatOpenRouter/LmChatOpenRouter.node.js", "dist/nodes/llms/LmChatXAiGrok/LmChatXAiGrok.node.js", "dist/nodes/llms/LMChatOpenAi/LmChatOpenAi.node.js", "dist/nodes/llms/LMOpenAi/LmOpenAi.node.js", "dist/nodes/llms/LMCohere/LmCohere.node.js", "dist/nodes/llms/LMOllama/LmOllama.node.js", "dist/nodes/llms/LMOpenHuggingFaceInference/LmOpenHuggingFaceInference.node.js", "dist/nodes/mcp/McpClientTool/McpClientTool.node.js", "dist/nodes/mcp/McpTrigger/McpTrigger.node.js", "dist/nodes/memory/MemoryBufferWindow/MemoryBufferWindow.node.js", "dist/nodes/memory/MemoryMotorhead/MemoryMotorhead.node.js", "dist/nodes/memory/MemoryPostgresChat/MemoryPostgresChat.node.js", "dist/nodes/memory/MemoryMongoDbChat/MemoryMongoDbChat.node.js", "dist/nodes/memory/MemoryRedisChat/MemoryRedisChat.node.js", "dist/nodes/memory/MemoryManager/MemoryManager.node.js", "dist/nodes/memory/MemoryChatRetriever/MemoryChatRetriever.node.js", "dist/nodes/memory/MemoryXata/MemoryXata.node.js", "dist/nodes/memory/MemoryZep/MemoryZep.node.js", "dist/nodes/output_parser/OutputParserAutofixing/OutputParserAutofixing.node.js", "dist/nodes/output_parser/OutputParserItemList/OutputParserItemList.node.js", "dist/nodes/output_parser/OutputParserStructured/OutputParserStructured.node.js", "dist/nodes/rerankers/RerankerCohere/RerankerCohere.node.js", "dist/nodes/retrievers/RetrieverContextualCompression/RetrieverContextualCompression.node.js", "dist/nodes/retrievers/RetrieverVectorStore/RetrieverVectorStore.node.js", "dist/nodes/retrievers/RetrieverMultiQuery/RetrieverMultiQuery.node.js", "dist/nodes/retrievers/RetrieverWorkflow/RetrieverWorkflow.node.js", "dist/nodes/text_splitters/TextSplitterCharacterTextSplitter/TextSplitterCharacterTextSplitter.node.js", "dist/nodes/text_splitters/TextSplitterRecursiveCharacterTextSplitter/TextSplitterRecursiveCharacterTextSplitter.node.js", "dist/nodes/text_splitters/TextSplitterTokenSplitter/TextSplitterTokenSplitter.node.js", "dist/nodes/tools/ToolCalculator/ToolCalculator.node.js", "dist/nodes/tools/ToolCode/ToolCode.node.js", "dist/nodes/tools/ToolHttpRequest/ToolHttpRequest.node.js", "dist/nodes/tools/ToolSearXng/ToolSearXng.node.js", "dist/nodes/tools/ToolSerpApi/ToolSerpApi.node.js", "dist/nodes/tools/ToolThink/ToolThink.node.js", "dist/nodes/tools/ToolVectorStore/ToolVectorStore.node.js", "dist/nodes/tools/ToolWikipedia/ToolWikipedia.node.js", "dist/nodes/tools/ToolWolframAlpha/ToolWolframAlpha.node.js", "dist/nodes/tools/ToolWorkflow/ToolWorkflow.node.js", "dist/nodes/trigger/ManualChatTrigger/ManualChatTrigger.node.js", "dist/nodes/trigger/ChatTrigger/ChatTrigger.node.js", "dist/nodes/vector_store/VectorStoreInMemory/VectorStoreInMemory.node.js", "dist/nodes/vector_store/VectorStoreInMemoryInsert/VectorStoreInMemoryInsert.node.js", "dist/nodes/vector_store/VectorStoreInMemoryLoad/VectorStoreInMemoryLoad.node.js", "dist/nodes/vector_store/VectorStoreMilvus/VectorStoreMilvus.node.js", "dist/nodes/vector_store/VectorStoreMongoDBAtlas/VectorStoreMongoDBAtlas.node.js", "dist/nodes/vector_store/VectorStorePGVector/VectorStorePGVector.node.js", "dist/nodes/vector_store/VectorStorePinecone/VectorStorePinecone.node.js", "dist/nodes/vector_store/VectorStorePineconeInsert/VectorStorePineconeInsert.node.js", "dist/nodes/vector_store/VectorStorePineconeLoad/VectorStorePineconeLoad.node.js", "dist/nodes/vector_store/VectorStoreQdrant/VectorStoreQdrant.node.js", "dist/nodes/vector_store/VectorStoreSupabase/VectorStoreSupabase.node.js", "dist/nodes/vector_store/VectorStoreSupabaseInsert/VectorStoreSupabaseInsert.node.js", "dist/nodes/vector_store/VectorStoreSupabaseLoad/VectorStoreSupabaseLoad.node.js", "dist/nodes/vector_store/VectorStoreWeaviate/VectorStoreWeaviate.node.js", "dist/nodes/vector_store/VectorStoreZep/VectorStoreZep.node.js", "dist/nodes/vector_store/VectorStoreZepInsert/VectorStoreZepInsert.node.js", "dist/nodes/vector_store/VectorStoreZepLoad/VectorStoreZepLoad.node.js", "dist/nodes/ToolExecutor/ToolExecutor.node.js", "dist/nodes/ModelSelector/ModelSelector.node.js"]}, "devDependencies": {"@types/basic-auth": "catalog:", "@types/cheerio": "^0.22.15", "@types/html-to-text": "^9.0.1", "@types/json-schema": "^7.0.15", "@types/mime-types": "^2.1.0", "@types/pg": "^8.11.6", "@types/sanitize-html": "^2.11.0", "@types/temp": "^0.9.1", "fast-glob": "catalog:", "n8n-core": "workspace:*", "tsup": "catalog:", "jest-mock-extended": "^3.0.4"}, "dependencies": {"@aws-sdk/client-sso-oidc": "3.808.0", "@azure/identity": "4.3.0", "@getzep/zep-cloud": "1.0.12", "@getzep/zep-js": "0.9.0", "@google-ai/generativelanguage": "2.6.0", "@google-cloud/resource-manager": "5.3.0", "@google/generative-ai": "0.21.0", "@huggingface/inference": "2.8.0", "@langchain/anthropic": "catalog:", "@langchain/aws": "0.1.11", "@langchain/cohere": "0.3.4", "@langchain/community": "catalog:", "@langchain/core": "catalog:", "@langchain/google-genai": "0.2.13", "@langchain/google-vertexai": "0.2.13", "@langchain/groq": "0.2.3", "@langchain/mistralai": "0.2.1", "@langchain/mongodb": "^0.1.0", "@langchain/ollama": "0.2.3", "@langchain/openai": "catalog:", "@langchain/pinecone": "0.2.0", "@langchain/qdrant": "0.1.2", "@langchain/redis": "0.1.1", "@langchain/textsplitters": "0.1.0", "@langchain/weaviate": "0.2.0", "@modelcontextprotocol/sdk": "1.12.0", "@mozilla/readability": "0.6.0", "@n8n/client-oauth2": "workspace:*", "@n8n/json-schema-to-zod": "workspace:*", "@n8n/typeorm": "0.3.20-12", "@n8n/typescript-config": "workspace:*", "@n8n/vm2": "3.9.25", "@pinecone-database/pinecone": "^5.0.2", "@qdrant/js-client-rest": "1.14.1", "@supabase/supabase-js": "2.49.9", "@xata.io/client": "0.28.4", "@zilliz/milvus2-sdk-node": "^2.5.7", "basic-auth": "catalog:", "cheerio": "1.0.0", "cohere-ai": "7.14.0", "d3-dsv": "2.0.0", "epub2": "3.0.2", "form-data": "catalog:", "generate-schema": "2.6.0", "html-to-text": "9.0.5", "https-proxy-agent": "catalog:", "js-tiktoken": "^1.0.12", "jsdom": "23.0.1", "langchain": "0.3.29", "lodash": "catalog:", "mammoth": "1.7.2", "mime-types": "2.1.35", "mongodb": "6.11.0", "n8n-nodes-base": "workspace:*", "n8n-workflow": "workspace:*", "openai": "5.8.1", "pdf-parse": "1.1.1", "pg": "8.12.0", "proxy-from-env": "^1.1.0", "redis": "4.6.12", "sanitize-html": "2.12.1", "sqlite3": "5.1.7", "temp": "0.9.4", "tmp-promise": "3.0.3", "undici": "^6.21.0", "weaviate-client": "3.6.2", "zod": "catalog:", "zod-to-json-schema": "3.23.3"}}