services:
  librechat:
    environment:
      # Database connections (essential for startup)
      - MONGO_URI=mongodb://abacus_chat_mongodb:27017/LibreChat
      - MEILI_HOST=http://abacus_chat_meilisearch:7700
      # Endpoint configuration
      - ENDPOINTS=google,openRouter,ollama,custom,localAI,agents
      - DISABLE_OPENAI=true
      # Keep these settings for localAI to use
      - OPENAI_API_KEY=sk-dummy
      - OPENAI_API_BASE_URL=http://vllm:7860/v1
      # Google configuration
      - GOOGLE_KEY=AIzaSyCojF_Ym6QA5mFmOhjDs8B-kcUoST07x7E
      - GOOGLE_MODELS=gemini-2.5-flash-preview-04-17,gemini-2.5-pro-preview-05-06
      - GOOGLE_SAFETY_SEXUALLY_EXPLICIT=BLOCK_ONLY_HIGH
      - GOOGLE_SAFETY_HATE_SPEECH=BLOCK_ONLY_HIGH
      - GOOGLE_SAFETY_HARASSMENT=BLOCK_ONLY_HIGH
      - GOOGLE_SAFETY_DANGEROUS_CONTENT=BLOCK_ONLY_HIGH
