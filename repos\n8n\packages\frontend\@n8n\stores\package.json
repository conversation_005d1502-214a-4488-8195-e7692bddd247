{"name": "@n8n/stores", "type": "module", "version": "1.10.0", "files": ["dist"], "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./*": {"types": "./dist/*.d.ts", "import": "./dist/*.js", "require": "./dist/*.cjs"}}, "scripts": {"dev": "vite", "build": "pnpm run typecheck && tsup", "preview": "vite preview", "typecheck": "vue-tsc --noEmit", "test": "vitest run", "test:dev": "vitest --silent=false", "lint": "eslint src --quiet", "lintfix": "eslint src --fix", "format": "biome format --write . && prettier --write . --ignore-path ../../../../.prettierignore", "format:check": "biome ci . && prettier --check . --ignore-path ../../../../.prettierignore"}, "dependencies": {"n8n-workflow": "workspace:*"}, "devDependencies": {"@n8n/eslint-config": "workspace:*", "@n8n/typescript-config": "workspace:*", "@n8n/vitest-config": "workspace:*", "@testing-library/jest-dom": "catalog:frontend", "@testing-library/user-event": "catalog:frontend", "@testing-library/vue": "catalog:frontend", "@vitejs/plugin-vue": "catalog:frontend", "@vue/tsconfig": "catalog:frontend", "@vueuse/core": "catalog:frontend", "pinia": "catalog:frontend", "vue": "catalog:frontend", "tsup": "catalog:", "typescript": "catalog:", "vite": "catalog:", "vitest": "catalog:", "vue-tsc": "catalog:frontend"}, "peerDependencies": {"@vueuse/core": "catalog:frontend", "pinia": "catalog:frontend", "vue": "catalog:frontend"}, "license": "See LICENSE.md file in the root of the repository"}