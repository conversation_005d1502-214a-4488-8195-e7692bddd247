networks:
  abacus-network:
    external: true
    driver_opts:
      com.docker.network.bridge.enable_icc: "true"
      com.docker.network.bridge.enable_ip_masquerade: "true"
      com.docker.network.driver.mtu: "1500"
  cloud:
    external: true
    driver_opts:
      com.docker.network.bridge.enable_icc: "true"
      com.docker.network.bridge.enable_ip_masquerade: "true"
      com.docker.network.driver.mtu: "1500"

services:
  tailscale:
    image: tailscale/tailscale:latest
    container_name: tailscale
    hostname: ${TS_HOSTNAME:-abacus-server}
    volumes:
      - ./data/tailscale/varlib:/var/lib/tailscale
      - /dev/net/tun:/dev/net/tun
    cap_add:
      - NET_ADMIN
      - NET_RAW
    environment:
      - TS_AUTHKEY=${TS_AUTHKEY}
      - TS_STATE_DIR=/var/lib/tailscale
      - TS_USERSPACE=false
      - TS_ROUTES=**********/16,**********/16,**********/16,**********/16  # Advertise Docker networks
      - TS_EXTRA_ARGS=--advertise-routes=**********/16,**********/16,**********/16,**********/16 --accept-routes
    restart: unless-stopped
    network_mode: host
    privileged: true

  open-webui:
    image: ghcr.io/open-webui/open-webui:dev-cuda
    container_name: open_webui # Changed to valid name
    ports:
      - "3000:8080"
    environment:
      - ENABLE_WEB_SEARCH=true
      - WEB_SEARCH_ENGINE=searxng
      - WEB_SEARCH_RESULT_COUNT=5
      - WEB_SEARCH_CONCURRENT_REQUESTS=10
      - SEARXNG_QUERY_URL=http://searxng:8080/search
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
      - CUDA_CACHE_DISABLE=0
    volumes:
      - ./data/open_webui:/app/backend/data # Relative path for persistent data
    tmpfs:
      - /tmp:size=1G,noexec,nosuid,nodev
      - /var/tmp:size=512M,noexec,nosuid,nodev
    restart: always # Changed from unless-stopped based on previous setup
    init: true
    security_opt:
      - no-new-privileges:true
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    deploy: # For GPU support
      resources:
        limits:
          cpus: '6.0'
          memory: 12G
        reservations:
          cpus: '2.0'
          memory: 4G
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    depends_on:
      searxng:
        condition: service_healthy
    networks:
      - abacus-network


  app:
    image: itzcrazykns1337/perplexica:main
    container_name: perplexica_search # Changed back to valid name
    ports:
      - "3001:3000" # Port from docker run command
    environment:
      - SEARXNG_API_URL=http://searxng:8080
    volumes:
      - backend-dbstore:/home/<USER>/data # Named volume for DB
      - uploads:/home/<USER>/uploads # Named volume for uploads
      - ./data/perplexica/config.toml:/home/<USER>/config.toml # Relative path for config
    tmpfs:
      - /tmp:size=512M,noexec,nosuid,nodev
    restart: unless-stopped
    init: true
    security_opt:
      - no-new-privileges:true
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    depends_on:
      searxng:
        condition: service_healthy
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.25'
          memory: 512M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - abacus-network

  searxng:
    image: docker.io/searxng/searxng:latest
    container_name: searxng
    ports:
      - "4000:8080"
    volumes:
      - ./data/searxng:/etc/searxng # Relative path for config
    tmpfs:
      - /tmp:size=512M,noexec,nosuid,nodev
      - /var/cache:size=256M,noexec,nosuid,nodev
    restart: unless-stopped
    init: true
    security_opt:
      - no-new-privileges:true
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 3G
        reservations:
          cpus: '0.5'
          memory: 1G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/search?q=test&format=json"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - abacus-network

  n8n:
    image: n8nio/n8n:next
    container_name: n8n
    ports:
      - "5678:5678"
    environment:
      - NODE_ENV=production
      - N8N_COMMUNITY_PACKAGES_ALLOW_TOOL_USAGE=true
    volumes:
      - ./data/n8n:/home/<USER>/.n8n # Relative path for n8n data
    tmpfs:
      - /tmp:size=512M,noexec,nosuid,nodev
    restart: unless-stopped
    init: true
    security_opt:
      - no-new-privileges:true
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.25'
          memory: 512M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5678/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - abacus-network


  # --- Supabase Services have been moved to supabase-compose.yaml ---

  # --- LibreChat Services Start ---

  librechat:
    image: ghcr.io/danny-avila/librechat:latest
    container_name: librechat
    ports:
      - "3080:3080"
    env_file:
      - ./repos/LibreChat/.env # Load environment variables from file
    environment:
      - MONGO_URI=mongodb://abacus_chat_mongodb:27017/LibreChat
      - MEILI_HOST=http://abacus_chat_meilisearch:7700
    depends_on:
      abacus_chat_mongodb:
        condition: service_healthy
      abacus_chat_meilisearch:
        condition: service_healthy
    volumes:
      - ./repos/LibreChat/uploads:/app/uploads
      - ./repos/LibreChat/logs:/app/logs
      - ./repos/LibreChat/librechat.yaml:/app/librechat.yaml
      - D:\ObsidianVault:/D/ObsidianVault
      - F:\iCloudDrive:/F/iCloudDrive
    tmpfs:
      - /tmp:size=1G,noexec,nosuid,nodev
    init: true
    security_opt:
      - no-new-privileges:true
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '0.5'
          memory: 1G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3080/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - abacus-network

  abacus_chat_mongodb: # Renamed from 'mongodb'
    container_name: abacus_chat_mongodb # Renamed container to avoid conflicts
    image: mongo
    restart: always
    # user: "${UID}:${GID}" # Removed for Windows compatibility
    volumes:
      - ./repos/LibreChat/data-node:/data/db # Adjusted path
    command: mongod --noauth --wiredTigerCacheSizeGB=2 --wiredTigerCollectionBlockCompressor=snappy --wiredTigerIndexPrefixCompression=true --journal --directoryperdb
    tmpfs:
      - /tmp:size=512M,noexec,nosuid,nodev
    init: true
    security_opt:
      - no-new-privileges:true
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    deploy:
      resources:
        limits:
          cpus: '1.5'
          memory: 3G
        reservations:
          cpus: '0.25'
          memory: 512M
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - abacus-network

  abacus_chat_meilisearch: # Renamed from 'meilisearch'
    container_name: abacus_chat_meilisearch # Renamed container to avoid conflicts
    env_file: # Added env_file directive
      - ./repos/LibreChat/.env # Point to the correct env file
    image: getmeili/meilisearch:v1.12.3
    restart: always
    # user: "${UID}:${GID}" # Removed for Windows compatibility
    environment:
      - MEILI_HOST=http://abacus_chat_meilisearch:7700 # Updated service name
      - MEILI_NO_ANALYTICS=true
      - MEILI_MASTER_KEY=${MEILI_MASTER_KEY} # This needs to be set in ./repos/LibreChat/.env
    volumes:
      - ./repos/LibreChat/meili_data_v1.12:/meili_data # Adjusted path
    tmpfs:
      - /tmp:size=512M,noexec,nosuid,nodev
    init: true
    security_opt:
      - no-new-privileges:true
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    deploy:
      resources:
        limits:
          cpus: '1.5'
          memory: 3G
        reservations:
          cpus: '0.25'
          memory: 512M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7700/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - abacus-network

  librechat_vectordb: # Renamed from 'vectordb'
    container_name: librechat_vectordb # Renamed container
    image: ankane/pgvector:latest
    environment:
      POSTGRES_DB: mydatabase
      POSTGRES_USER: myuser
      POSTGRES_PASSWORD: mypassword
      POSTGRES_SHARED_BUFFERS: 1GB
      POSTGRES_EFFECTIVE_CACHE_SIZE: 3GB
      POSTGRES_WORK_MEM: 64MB
      POSTGRES_MAINTENANCE_WORK_MEM: 256MB
    restart: always
    volumes:
      - librechat_pgdata:/var/lib/postgresql/data # Renamed volume
    tmpfs:
      - /tmp:size=512M,noexec,nosuid,nodev
    init: true
    security_opt:
      - no-new-privileges:true
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '0.5'
          memory: 1G
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U myuser -d mydatabase"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - abacus-network

  abacus_rag_api: # Renamed from 'rag_api'
    container_name: abacus_rag_api # Renamed container to avoid conflicts
    image: ghcr.io/danny-avila/librechat-rag-api-dev-lite:latest
    environment:
      - DB_HOST=librechat_vectordb # Updated service name
      - RAG_PORT=${RAG_PORT:-8000}
    depends_on:
      - librechat_vectordb # Updated dependency
    env_file:
      - ./repos/LibreChat/.env # Adjusted path
    networks:
      - abacus-network

  # --- Whisper Service for Audio Transcription ---
  whisper:
    image: onerahmet/openai-whisper-asr-webservice:latest-gpu
    container_name: whisper
    # Expose port 9000 directly
    ports:
      - "9000:9000"
    environment:
      - ASR_MODEL=base
      - ASR_ENGINE=faster_whisper
      - COMPUTE_TYPE=float16
      - DEVICE=cuda
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
      - CUDA_CACHE_DISABLE=0
    volumes:
      - ./data/whisper:/root/.cache/whisper
    tmpfs:
      - /tmp:size=1G,noexec,nosuid,nodev
    restart: unless-stopped
    init: true
    security_opt:
      - no-new-privileges:true
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    deploy:
      resources:
        limits:
          cpus: '4.0'
          memory: 6G
        reservations:
          cpus: '1.0'
          memory: 2G
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - abacus-network

  # --- vLLM Service for AI Model Inference ---
  vllm:
    build:
      context: .
      dockerfile: Dockerfile.vllm
    container_name: vllm
    ports:
      - "7860:7860"
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
      - CUDA_CACHE_DISABLE=0
    volumes:
      - ./data/vllm:/data
    tmpfs:
      - /tmp:size=2G,noexec,nosuid,nodev
    # Command is defined in the Dockerfile
    restart: unless-stopped
    init: true
    security_opt:
      - no-new-privileges:true
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    deploy:
      resources:
        limits:
          cpus: '8.0'
          memory: 16G
        reservations:
          cpus: '4.0'
          memory: 8G
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    networks:
      - abacus-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7860/v1/models"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s

  # --- Llama-cpp Service for AI Model Inference ---
  llama-cpp:
    container_name: llama-cpp
    image: ghcr.io/ggml-org/llama.cpp:server-cuda
    ports:
      - "8001:8000" # Default port for llama.cpp server
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
      - CUDA_CACHE_DISABLE=0
    command: -hf unsloth/Mistral-Small-3.2-24B-Instruct-2506-GGUF:UD-Q4_K_XL --port 8000 --host 0.0.0.0 --n-gpu-layers 99 --jinja -fa --temp 0.15
    tmpfs:
      - /tmp:size=1G,noexec,nosuid,nodev
    restart: unless-stopped
    init: true
    security_opt:
      - no-new-privileges:true
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    deploy:
      resources:
        limits:
          cpus: '4.0'
          memory: 8G
        reservations:
          cpus: '1.0'
          memory: 2G
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 90s
    networks:
      - abacus-network

  flowise:
    image: flowiseai/flowise
    restart: always
    environment:
      - PORT=3002
      - DATABASE_PATH=/root/.flowise
      - SECRETKEY_PATH=/root/.flowise
      - LOG_PATH=/root/.flowise/logs
      - BLOB_STORAGE_PATH=/root/.flowise/storage
      - CORS_ORIGINS=${CORS_ORIGINS}
      - IFRAME_ORIGINS=${IFRAME_ORIGINS}
      - FLOWISE_FILE_SIZE_LIMIT=${FLOWISE_FILE_SIZE_LIMIT}
      - DEBUG=${DEBUG}
      - DATABASE_TYPE=${DATABASE_TYPE}
      - DATABASE_PORT=${DATABASE_PORT}
      - DATABASE_HOST=${DATABASE_HOST}
      - DATABASE_NAME=${DATABASE_NAME}
      - DATABASE_USER=${DATABASE_USER}
      - DATABASE_PASSWORD=${DATABASE_PASSWORD}
      - DATABASE_SSL=${DATABASE_SSL}
      - DATABASE_SSL_KEY_BASE64=${DATABASE_SSL_KEY_BASE64}
      - FLOWISE_SECRETKEY_OVERWRITE=${FLOWISE_SECRETKEY_OVERWRITE}
      - SECRETKEY_STORAGE_TYPE=${SECRETKEY_STORAGE_TYPE}
      - SECRETKEY_AWS_ACCESS_KEY=${SECRETKEY_AWS_ACCESS_KEY}
      - SECRETKEY_AWS_SECRET_KEY=${SECRETKEY_AWS_SECRET_KEY}
      - SECRETKEY_AWS_REGION=${SECRETKEY_AWS_REGION}
      - SECRETKEY_AWS_NAME=${SECRETKEY_AWS_NAME}
      - LOG_LEVEL=${LOG_LEVEL}
      - MODEL_LIST_CONFIG_JSON=${MODEL_LIST_CONFIG_JSON}
      - GLOBAL_AGENT_HTTP_PROXY=${GLOBAL_AGENT_HTTP_PROXY}
      - GLOBAL_AGENT_HTTPS_PROXY=${GLOBAL_AGENT_HTTPS_PROXY}
      - GLOBAL_AGENT_NO_PROXY=${GLOBAL_AGENT_NO_PROXY}
      - DISABLED_NODES=${DISABLED_NODES}
      - MODE=${MODE}
      - WORKER_CONCURRENCY=${WORKER_CONCURRENCY}
      - QUEUE_NAME=${QUEUE_NAME}
      - QUEUE_REDIS_EVENT_STREAM_MAX_LEN=${QUEUE_REDIS_EVENT_STREAM_MAX_LEN}
      - REMOVE_ON_AGE=${REMOVE_ON_AGE}
      - REMOVE_ON_COUNT=${REMOVE_ON_COUNT}
      - REDIS_URL=${REDIS_URL}
      - REDIS_HOST=${REDIS_HOST}
      - REDIS_PORT=${REDIS_PORT}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_USERNAME=${REDIS_USERNAME}
      - REDIS_TLS=${REDIS_TLS}
      - REDIS_CERT=${REDIS_CERT}
      - REDIS_KEY=${REDIS_KEY}
      - REDIS_CA=${REDIS_CA}
      - REDIS_KEEP_ALIVE=${REDIS_KEEP_ALIVE}
      - ENABLE_BULLMQ_DASHBOARD=${ENABLE_BULLMQ_DASHBOARD}
    ports:
      - '3002:3002'
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3002/api/v1/ping']
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    volumes:
      - ./data/flowise:/root/.flowise
    entrypoint: /bin/sh -c "sleep 3; flowise start"
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.25'
          memory: 512M
    networks:
      - abacus-network

  crawl4ai:
    image: unclecode/crawl4ai:latest
    container_name: crawl4ai
    ports:
      - "11235:11235"
    env_file:
      - ./repos/crawl4ai/.llm.env
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY:-}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY:-}
      - GROQ_API_KEY=${GROQ_API_KEY:-}
      - TOGETHER_API_KEY=${TOGETHER_API_KEY:-}
      - MISTRAL_API_KEY=${MISTRAL_API_KEY:-}
      - GEMINI_API_TOKEN=${GEMINI_API_TOKEN:-}
    volumes:
      - /dev/shm:/dev/shm
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 1G
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11235/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    user: "appuser"
    networks:
      - abacus-network

  agent-zero:
    image: frdel/agent-zero-run
    container_name: agent-zero
    restart: unless-stopped
    ports:
      - "50001:80"
    volumes:
      - ./data/agent-zero:/a0
    env_file:
      - ./.env
    environment:
      - PROMPT_DIR=/a0/prompts # Explicitly set the prompt directory
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
      - CUDA_CACHE_DISABLE=0
    tmpfs:
      - /tmp:size=1G,noexec,nosuid,nodev
    init: true
    security_opt:
      - no-new-privileges:true
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    deploy:
      resources:
        limits:
          cpus: '4.0'
          memory: 8G
        reservations:
          cpus: '1.0'
          memory: 2G
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - abacus-network


  nextcloud:
    image: nextcloud
    container_name: nextcloud
    restart: unless-stopped
    networks:
      - cloud
    depends_on:
      nextclouddb:
        condition: service_healthy
      redis:
        condition: service_started
    ports:
      - 8084:80 # Changed to port 8084 to avoid conflicts
    volumes:
      - nextcloud_html:/var/www/html
      - nextcloud_custom_apps:/var/www/html/custom_apps
      - nextcloud_config:/var/www/html/config
      - E:/nextcloud_data:/var/www/html/data
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=America/Los_Angeles
      - MYSQL_DATABASE=nextcloud
      - MYSQL_USER=nextcloud
      - MYSQL_PASSWORD=dbpassword
      - MYSQL_HOST=nextclouddb
      - REDIS_HOST=redis
      - NEXTCLOUD_TRUSTED_DOMAINS=localhost abacus-server.tail0b0f33.ts.net ************* 127.0.0.1 **********/16 **********/16
      - NEXTCLOUD_OVERWRITECLIURL=http://*************:8084
      - APACHE_DISABLE_REWRITE_IP=1
      - TRUSTED_PROXIES=**********/16 **********/16
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 3G
        reservations:
          cpus: '0.5'
          memory: 1G
  nextclouddb:
    image: mariadb
    container_name: nextcloud-db
    restart: unless-stopped
    command: --transaction-isolation=READ-COMMITTED --binlog-format=ROW
    networks:
      - cloud
    volumes:
      - nextcloud_db:/var/lib/mysql
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=America/Los_Angeles
      - MYSQL_RANDOM_ROOT_PASSWORD=true
      - MYSQL_PASSWORD=dbpassword
      - MYSQL_DATABASE=nextcloud
      - MYSQL_USER=nextcloud
    healthcheck:
      test: ["CMD", "healthcheck.sh", "--connect", "--innodb_initialized"]
      start_period: 10s
      interval: 10s
      timeout: 5s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 3G
        reservations:
          cpus: '0.5'
          memory: 1G

  redis:
    image: redis:alpine
    container_name: redis
    volumes:
      - nextcloud_redis:/data
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 256M
    networks:
      - cloud

  mcpo:
    image: ghcr.io/open-webui/mcpo:latest
    container_name: mcpo
    ports:
      - "8000:8000" # Expose mcpo's default port
    volumes:
      - ./mcpo/config:/app/config
      - ./mcpo/data:/app/data # Mount volume for persistent memory data
      - C:\Users\<USER>\Nextcloud2:/app/nextcloud_data # Mount Nextcloud directory for desktop-commander access
    command: mcpo --config /app/config/config.json
    tmpfs:
      - /tmp:size=512M,noexec,nosuid,nodev
    restart: unless-stopped
    init: true
    security_opt:
      - no-new-privileges:true
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '0.5'
          memory: 1G
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - abacus-network

volumes:
  backend-dbstore: # Define named volume for perplexica DB
  uploads: # Define named volume for perplexica uploads
  # Note: The open-webui volume is now a bind mount, not a named volume here.
  db-config: # Supabase volume
  librechat_pgdata: # Renamed from pgdata2
  whisper_data: # Volume for whisper model cache
  vllm_data: # Volume for vLLM model cache
  flowise_data: # Volume for Flowise data
  nextcloud_html:
  nextcloud_custom_apps:
  nextcloud_config:
  nextcloud_db:
  nextcloud_redis:
  tailscale_socket: # Define named volume for Tailscale socket
  obsidian_vault: # Volume for Obsidian vault data
